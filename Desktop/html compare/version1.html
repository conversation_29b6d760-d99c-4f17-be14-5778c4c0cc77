<html xmlns="http://www.w3.org/1999/xhtml" lang="fr" class=" js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths js flexbox flexboxlegacy canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths"><head><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="DC.description" content="Rendez-vous santé Québec, pour prendre rendez-vous avec un professionnel de la santé."><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><title> Rendez-vous disponibles pour « consultation urgente » à partir du 12&nbsp;juin&nbsp;2025</title><link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/Styles/RadiosNCheckboxes.css">

    
    <style>
        .citizen-feedback-link {
            position: fixed;
            top: 47%;
            direction: rtl;
            margin-left: -14px;
            height: auto;
            padding: 10px 10px;
            display: flex;
            left: calc(100% - 20px);
            writing-mode: vertical-rl;
            transform: rotate(180deg);
            box-sizing: border-box !important;
            background-color: #2c88b7;
            color: white !important;
            border: none;
            border-radius: 0px 6px 6px 0px;
            z-index: 1000;
        }

        .citizen-feedback-link:hover {
            text-decoration: none;
            box-shadow: 0px 0px 35px 2px rgb(0, 0, 0, 24%);
            width: 42px;
            transition: width 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
        }
    </style>
<meta name="robots" content="noindex, nofollow"><script src="/Scripts/externals.1.1.9256.26351.min.js?v=OeujI1rSgH6qpvtAzZ9wMEBQgQ7kxrer6sEhFPXJMbk1"></script><style></style>
<link href="/Styles/externals.1.1.9256.26351.min.css?v=KT5JFZvLsKrzHhq-1kW-pP_03XN4Lwu1X7-Ke3rodmk1" rel="stylesheet">
<script src="/Scripts/RDVS.1.1.9256.26351.min.js?v=wZWQYozCdX2zmmXzt_CO3pqC_MKhgjcl8s1jkFUlRIQ1"></script>
<link href="/Styles/RDVS.1.1.9256.26351.min.css?v=JDdAp1t8SedfkffHheTxiVnBtjyXJpN9vRMMABr0Fik1" rel="stylesheet">
<link href="/Styles/styles-fr-CA.css?v=1.1.9256.26351" type="text/css" rel="stylesheet"><link rel="" href="/favicon.ico"><link rel="" sizes="114x114" href="/Images/favicon-114x114.png"><link rel="apple-touch-icon" sizes="72x72" href="/Images/favicon-152x152.png"><base type="https://rvsq.gouv.qc.ca/prendrerendezvous/Recherche.aspx"><link href="/Styles/RDVS.Assure-Recherche.1.1.9256.26351.min.css?v=9Ewo3fuk41Uu5YjXYraiIz8irCOKcVmUU7YvDRV5hGs1" rel="stylesheet">
<script src="/Scripts/RDVS.Assure-Recherche.1.1.9256.26351.min.js?v=3Q-rGI8tCCST-RHqS0bDe1B-Nuwwar4qnUMXXpDfRK41"></script><style></style>
<link href="/Styles/RDVS.AssureMP.1.1.9256.26351.min.css?v=1hgc7Zfw-5mz_sx1ep8hFCNN9v4TAoGV5i35dVYxgIw1" rel="stylesheet">
<link href="/Styles/RDVS.PIV.1.1.9256.26351.min.css?v=UZThOrYNIBy_DNwwA7dLr3fonCYZaDN2d8k_TqpkcVU1" rel="stylesheet">
<script src="/Scripts/RDVS.CookiesModal.1.1.9256.26351.min.js?v=LK2Ban5SLHfaPj0hyjhaSIf7TTT0TPdUDOwRHVK7xOI1"></script>
<base type="https://rvsq.gouv.qc.ca/prendrerendezvous/Recherche.aspx"></head>
<body class="noPadding assure-body" style="cursor: default;"><style>.btn-primary {text-shadow: none!important;background-image: none!important;background-color: #08C!important;color: #fff!important;}</style>
    <a id="ctl00_CitizenFeedbackLink" class="citizen-feedback-link" href="https://interceptum.com/s/fr/RVSQ" target="_blank">Donner mon avis</a>
    <form method="post" action="./Recherche.aspx" id="aspnetForm">
<div class="aspNetHidden">
<input type="hidden" name="RDVSUserId" id="RDVSUserId" value="0">
<input type="hidden" name="RDVSPageInfo" id="RDVSPageInfo" value="{ &quot;absoluteUrl&quot;:&quot;//rvsq.gouv.qc.ca:80/&quot;, &quot;rootUrl&quot;:&quot;/&quot;, &quot;sessionTimeout&quot;:0, &quot;isMobileDevice&quot;:false, &quot;forceHttps&quot;:0, &quot;userIP&quot;:&quot;*************&quot;, &quot;version&quot;:&quot;1.1.9256.26351&quot;, &quot;versionDate&quot;:&quot;2025-05-06 16:01:56&quot;, &quot;features&quot;:{}, &quot;standardDateFormat&quot;:&quot;dd-MM-yyyy&quot;, &quot;datePickerFormat&quot;:&quot;dd-mm-yyyy&quot;, &quot;stripePublishableKey&quot;:&quot;&quot;, &quot;tps&quot;:0, &quot;tvq&quot;:0 }">
<input type="hidden" name="RDVSDataServices" id="RDVSDataServices" value="{ &quot;dataApiUrl&quot;:&quot;/api2/&quot; }">
<input type="hidden" name="EnableUserTracking" id="EnableUserTracking" value="0">
<input type="hidden" name="RDVSCSRFToken" id="RDVSCSRFToken" value="-jfZ2upXlsHIDKwL3ZaCloGwevfBY2v5n8I7MQPDcSU1">
<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="/wEPDwULLTExODM1MDY1NzJkGAEFHl9fQ29udHJvbHNSZXF1aXJlUG9zdEJhY2tLZXlfXxYOBVJjdGwwMCRDb250ZW50UGxhY2VIb2xkZXJNUCRDbGllbnRJbmZvcm1hdGlvbkZvcm0xJElkZW50aWZpY2F0aW9uU3ViVmlldyRNYWxlR2VuZGVyBVRjdGwwMCRDb250ZW50UGxhY2VIb2xkZXJNUCRDbGllbnRJbmZvcm1hdGlvbkZvcm0xJElkZW50aWZpY2F0aW9uU3ViVmlldyRGZW1hbGVHZW5kZXIFSWN0bDAwJENvbnRlbnRQbGFjZUhvbGRlck1QJENsaWVudEluZm9ybWF0aW9uRm9ybTEkQ2xpZW50SW5mb19FbWFpbENoZWNrZWQFSGN0bDAwJENvbnRlbnRQbGFjZUhvbGRlck1QJENsaWVudEluZm9ybWF0aW9uRm9ybTEkQ2xpZW50SW5mb19UZXh0Q2hlY2tlZAVJY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyTVAkQ2xpZW50SW5mb3JtYXRpb25Gb3JtMSRDbGllbnRJbmZvX1Bob25lQ2hlY2tlZAVGY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyTVAkQ2xpZW50SW5mb3JtYXRpb25Gb3JtMSRDbGllbnRJbmZvX0xhbmd1YWdlRgVGY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyTVAkQ2xpZW50SW5mb3JtYXRpb25Gb3JtMSRDbGllbnRJbmZvX0xhbmd1YWdlRQVpY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyTVAkQXNzdXJlVXBkYXRlQXBwb2ludG1lbnQkQ2xpZW50SW5mb3JtYXRpb25Gb3JtJElkZW50aWZpY2F0aW9uU3ViVmlldyRNYWxlR2VuZGVyBWtjdGwwMCRDb250ZW50UGxhY2VIb2xkZXJNUCRBc3N1cmVVcGRhdGVBcHBvaW50bWVudCRDbGllbnRJbmZvcm1hdGlvbkZvcm0kSWRlbnRpZmljYXRpb25TdWJWaWV3JEZlbWFsZUdlbmRlcgVgY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyTVAkQXNzdXJlVXBkYXRlQXBwb2ludG1lbnQkQ2xpZW50SW5mb3JtYXRpb25Gb3JtJENsaWVudEluZm9fRW1haWxDaGVja2VkBV9jdGwwMCRDb250ZW50UGxhY2VIb2xkZXJNUCRBc3N1cmVVcGRhdGVBcHBvaW50bWVudCRDbGllbnRJbmZvcm1hdGlvbkZvcm0kQ2xpZW50SW5mb19UZXh0Q2hlY2tlZAVgY3RsMDAkQ29udGVudFBsYWNlSG9sZGVyTVAkQXNzdXJlVXBkYXRlQXBwb2ludG1lbnQkQ2xpZW50SW5mb3JtYXRpb25Gb3JtJENsaWVudEluZm9fUGhvbmVDaGVja2VkBV1jdGwwMCRDb250ZW50UGxhY2VIb2xkZXJNUCRBc3N1cmVVcGRhdGVBcHBvaW50bWVudCRDbGllbnRJbmZvcm1hdGlvbkZvcm0kQ2xpZW50SW5mb19MYW5ndWFnZUYFXWN0bDAwJENvbnRlbnRQbGFjZUhvbGRlck1QJEFzc3VyZVVwZGF0ZUFwcG9pbnRtZW50JENsaWVudEluZm9ybWF0aW9uRm9ybSRDbGllbnRJbmZvX0xhbmd1YWdlRYzvQ4h9/x3Ql4/EG3sHpqZ2yH8WaGeSBci5SsZoC6+9">
</div>


<script type="text/javascript">
//<![CDATA[
if( typeof jQuery !== 'undefined' && typeof Common !== 'undefined') labels = JSON.parse(Common.decodeHtml('{&quot;datatableLabels&quot;: {    &quot;oAria&quot;: {        &quot;sSortAscending&quot;: &quot;Trier la colonne en ordre croissant&quot;,        &quot;sSortDescending&quot;: &quot;Trier la colonne en ordre décroissant&quot;    },    &quot;oPaginate&quot;: {        &quot;sFirst&quot;: &quot;Début&quot;,        &quot;sLast&quot;: &quot;Fin&quot;,        &quot;sNext&quot;: &quot;Suivant&quot;,        &quot;sPrevious&quot;: &quot;Précédent&quot;    },    &quot;sEmptyTable&quot;: &quot;&quot;,    &quot;sInfo&quot;: &quot;_START_ à _END_ de _TOTAL_&quot;,    &quot;sInfoEmpty&quot;: &quot;Aucun item&quot;,    &quot;sInfoFiltered&quot;: &quot;(filtrés de _MAX_ items  totaux)&quot;,    &quot;sInfoPostFix&quot;: &quot;&quot;,    &quot;sInfoThousands&quot;: &quot; &quot;,    &quot;sLengthMenu&quot;: &quot;_MENU_&quot;,    &quot;sLoadingRecords&quot;: &quot;...&quot;,    &quot;sProcessing&quot;: &quot;Chargement...&quot;,    &quot;sSearch&quot;: &quot;Rechercher&quot;,    &quot;sUrl&quot;: &quot;&quot;,    &quot;sFormatInputTooShort&quot;: &quot;Veuillez entrer {0} autre(s) caractère(s)&quot;,    &quot;sZeroRecords&quot;: &quot;Aucun résultat trouvé&quot;,    &quot;sZeroRecordsModal&quot;: &quot;Aucun modèle n&#8217;est disponible.&quot;,    &quot;sZeroRecordsLocation&quot;: &quot;Aucun lieu n&#8217;est disponible.&quot;},&quot;datatablesearchLabels&quot;: {    &quot;oAria&quot;: {        &quot;sSortAscending&quot;: &quot;Trier la colonne en ordre croissant&quot;,        &quot;sSortDescending&quot;: &quot;Trier la colonne en ordre décroissant&quot;    },    &quot;oPaginate&quot;: {        &quot;sFirst&quot;: &quot;Début&quot;,        &quot;sLast&quot;: &quot;Fin&quot;,        &quot;sNext&quot;: &quot;Suivant&quot;,        &quot;sPrevious&quot;: &quot;Précédent&quot;    },    &quot;sEmptyTable&quot;: &quot;&quot;,    &quot;sInfo&quot;: &quot;_START_ à _END_ de _TOTAL_&quot;,    &quot;sInfoEmpty&quot;: &quot;Aucun item&quot;,    &quot;sInfoFiltered&quot;: &quot;(filtrés de _MAX_ items  totaux)&quot;,    &quot;sInfoPostFix&quot;: &quot;&quot;,    &quot;sInfoThousands&quot;: &quot; &quot;,    &quot;sLengthMenu&quot;: &quot;_MENU_&quot;,    &quot;sLoadingRecords&quot;: &quot;...&quot;,    &quot;sProcessing&quot;: &quot;Recherche en cours. Veuillez patienter.&quot;,    &quot;sSearch&quot;: &quot;Rechercher&quot;,    &quot;sUrl&quot;: &quot;&quot;,    &quot;sFormatInputTooShort&quot;: &quot;Veuillez entrer {0} autre(s) caractère(s)&quot;,    &quot;sZeroRecords&quot;: &quot;Aucun résultat trouvé&quot;,    &quot;sZeroRecordsModal&quot;: &quot;Aucun modèle n&#8217;est disponible.&quot;,    &quot;sZeroRecordsLocation&quot;: &quot;Aucun lieu n&#8217;est disponible.&quot;},&quot;select2Labels&quot;: {    &quot;noMatches&quot;: &quot;Aucun item trouvé&quot;,    &quot;inputTooShort&quot;: &quot;Veuillez entrer {0} autre caractère{1}&quot;,    &quot;inputTooLong&quot;: &quot;Veuillez effacer {0} caractère{1}&quot;,    &quot;selectionTooBig&quot;: &quot;Vous pouvez sélectionner {0} item{1} maximum&quot;,    &quot;loadMore&quot;: &quot;Chargement en cours...&quot;,    &quot;searching&quot;: &quot;Recherche...&quot;},&quot;activeCulture&quot;: &quot;fr-CA&quot;,&quot;activeLanguage&quot;: &quot;fr&quot;,&quot;Accessibility_OpensInANewWindow&quot;: &quot;Ouvre dans une nouvelle fenêtre&quot;,&quot;Accessibility_IsAvailableForAppt&quot;: &quot;Est disponible&quot;,&quot;Button_Bold&quot;: &quot;G&quot;,&quot;Button_Italic&quot;: &quot;I&quot;,&quot;Button_Underline&quot;: &quot;S&quot;,&quot;Button_InsertLink&quot;: &quot;Insérer une lien&quot;,&quot;Button_EditHtml&quot;: &quot;Modifier le HTML&quot;,&quot;Button_Cancel&quot;: &quot;Annuler&quot;,&quot;Button_Delete&quot;: &quot;Supprimer&quot;,&quot;Button_Deactivate&quot;: &quot;Désactiver&quot;,&quot;Button_OK&quot;: &quot;OK&quot;,&quot;Button_Clear&quot;: &quot;Enlever&quot;,&quot;Label_consultation_reason_description&quot;: &quot;Description de la raison choisie&quot;,&quot;Label_default_consultation_reason&quot;: &quot;Choisir une raison&quot;,&quot;Button_CancelActivity_SelectedOnly&quot;: &quot;Annuler ce rendez-vous seulement&quot;,&quot;Button_CancelActivity_SelectedAndFutureRecurrences&quot;: &quot;Annuler ce rendez-vous et toutes les récurrences futures&quot;,&quot;Button_CancelActivity&quot;: &quot;Annuler ce rendez-vous&quot;,&quot;Button_Delete_Permission&quot;: &quot;Button_Delete_Permission&quot;,&quot;Label_MapYouAreHere&quot;: &quot;Vous êtes ici&quot;,&quot;Label_MyCurrentLocation&quot;: &quot;Ma position actuelle&quot;,&quot;Label_AccountOwner&quot;: &quot;Propriétaire du compte&quot;,&quot;Label_Subscription&quot;: &quot;Abonnement&quot;,&quot;Column_Expiration&quot;: &quot;Expiration&quot;,&quot;Column_Creation&quot;: &quot;Création&quot;,&quot;Label_Captcha&quot;: &quot;À des fins de sécurité, saisissez les caractères ci-dessus.&quot;,&quot;ErrorMessage_CaptchaInvalid&quot;: &quot;Les caractères saisis ne correspondent pas.&quot;,&quot;ErrorMessage_AtLeastOneCommunicationModeIsRequired&quot;: &quot;Veuillez indiquer au moins un mode de communication.&quot;,&quot;Message_WarningTimerEnd&quot;: &quot;Attention! Le délai de 3 minutes pour confirmer votre rendez-vous sera bientôt terminé. Désirez-vous prolonger ce délai?&quot;,&quot;Label_Type_GMF&quot;: &quot;GMF&quot;,&quot;Label_Type_MF&quot;: &quot;MF&quot;,&quot;Label_Type_GEN&quot;: &quot;GEN&quot;,&quot;Label_Type_GMF_Full&quot;: &quot;GMF (Groupe de Médecine de Famille)&quot;,&quot;Label_Type_MF_Full&quot;: &quot;MF (Médecin de Famille)&quot;,&quot;Label_Type_GEN_Full&quot;: &quot;GEN (Général)&quot;,&quot;Label_ConsultationReason_ALL&quot;: &quot;Toutes les raisons&quot;,&quot;ErrorMessage_TimeAvailabilityTemplate_Name_Empty&quot;: &quot;Veuillez indiquer le nom du modèle d&#8217;horaire.&quot;,&quot;ErrorMessage_TimeAvailabilityTemplate_NoTA&quot;: &quot;Veuillez ajouter au moins une plage de disponibilité.&quot;,&quot;ErrorMessage_TimeAvailabilityTemplate_Professional&quot;: &quot;Veuillez inscrire au moins un professionnel.&quot;,&quot;ErrorMessage_CannotCreateAvailabilityOver1Day&quot;: &quot;Impossible de créer une plage de disponibilité qui s&#39;étend sur plus d&#39;une journée.&quot;,&quot;Button_PayWithAmount&quot;: &quot;Payer {{amount}}&quot;,&quot;Button_Buy&quot;: &quot;Acheter&quot;,&quot;Label_XPrepaidAppointments&quot;: &quot;{0} rendez-vous prépayés&quot;,&quot;Message_BuyPrepaidAppointmentsSuccessful&quot;: &quot;Vous possédez maintenant un total de {0} rendez-vous prépayés dans votre compte.&quot;,&quot;Message_RemainingClientAppointments&quot;: &quot;Vous pouvez créer {0} autres rendez-vous ce mois-ci.&quot;,&quot;Message_ModificationsCancelled&quot;: &quot;Si vous avez effectué des modifications, elles ont été annulées.&quot;,&quot;DataTable_sZeroRecords_Locations&quot;: &quot;Aucun lieu trouvé&quot;,&quot;DataTable_sZeroRecords_Services&quot;: &quot;Aucun service trouvé&quot;,&quot;DataTable_sZeroRecords_Perms&quot;: &quot;Aucune permission trouvée&quot;,&quot;sZeroRecordsModal&quot;: &quot;Aucun modèle n&#8217;est disponible.&quot;,&quot;sZeroRecordsLocation&quot;: &quot;Aucun lieu n&#8217;est disponible.&quot;,&quot;DataTable_sInfoEmpty_Perms&quot;: &quot;Aucune permission&quot;,&quot;Tooltip_YourClinic&quot;: &quot; Votre lieu de suivi&quot;,&quot;Tooltip_YourClinic_811&quot;: &quot;Lieu de suivi de la personne&quot;,&quot;Label_FirstAvailableDate&quot;: &quot;Première date disponible :&quot;,&quot;quickSearch_NoResult_Label&quot;: &quot;Aucune disponibilité.&quot;,&quot;quickSearch_NoResultWithDate_Label&quot;: &quot;Aucune disponibilité pour le&quot;,&quot;warning_remove_permissions_administrator&quot;: &quot;Souhaitez-vous vraiment retirer la permission d&#8217;administrateur local de cet employé?&quot;,&quot;warning_delete_administrator&quot;: &quot;warning_delete_administrator&quot;,&quot;ErrorMessage_FileTooBig&quot;: &quot;La taille du fichier est trop grande. La taille maximale est {0} Ko&quot;,&quot;ErrorMessage_FileTooBigMB&quot;: &quot;La taille du fichier est trop grande. La taille maximale est {0} Mo&quot;,&quot;ErrorMessage_UnexpectedServerError&quot;: &quot;Une erreur s&#39;est produite dans le traitement de votre demande. Veuillez réessayer un peu plus tard.&quot;,&quot;Label_PleaseSelectEmployee&quot;: &quot;Sélectionnez un employé...&quot;,&quot;ErrorMessage_SelectScheduleEmployee&quot;: &quot;Veuillez sélectionner un professionnel de la santé.&quot;,&quot;Label_SelectScheduleEmployee&quot;: &quot;Choisir un professionnel de la santé&quot;,&quot;Label_Inactive_Service&quot;: &quot;Inactif&quot;,&quot;Label_IncompleteAvailability&quot;: &quot;Plage incomplète&quot;,&quot;Label_NavigationInterceptorTitle&quot;: &quot;Modification en cours...&quot;,&quot;Label_NabigationInterceptorTitle_EditSchedule&quot;: &quot;Mode édition de l&#39;horaire...&quot;,&quot;Content_NavigationInterceptor&quot;: &quot;Pour quitter le mode horaire, vous devez sélectionner « J&#39;ai terminé. ».&quot;,&quot;Label_NavigationInterceptor_EditScheduleModal&quot;: &quot;Pour appliquer un modèle d&#8217;horaire, vous devez sélectionner « Appliquer un modèle d&#8217;horaire » dans le menu « Actions ».&quot;,&quot;Label_EntrerpriseMessage_Email&quot;: &quot;Message de la clinique&quot;,&quot;Message_SupportRefreshToolTip&quot;: &quot;Réinitialiser toutes les données.&quot;,&quot;btn_Add_Time&quot;: &quot;Oui&quot;,&quot;btn_DoNot_Add_Time&quot;: &quot;Non&quot;,&quot;ErrorMessage_ActivityIsInPast&quot;: &quot;Vous ne pouvez pas enregistrer ou annuler ce rendez-vous, car sa date est passée depuis plus de 90 jours.&quot;,&quot;ErrorMessage_AvailabilityIsInPast&quot;: &quot;Vous ne pouvez pas enregistrer ou supprimer une plage de disponibilité dans le passé.&quot;,&quot;ErrorMessage_CannotDeleteLocation_Other&quot;: &quot;Ce lieu ne peut être retiré du profil de l&#39;employé, car d&#39;autres employés possèdent des droits sur des agendas de professionnels de la santé qui y pratiquent. Veuillez modifier les permissions des employés qui ont ces droits avant de procéder.&quot;,&quot;ErrorMessage_CannotDeleteLocation_Target&quot;: &quot;Ce lieu ne peut être retiré du profil de l&#39;employé, car ce dernier possède des droits sur des agendas de professionnels de la santé qui y pratiquent. Veuillez retirer les droits de cet employé sur les agendas liés à ce lieu avant de procéder.&quot;,&quot;ErrorMessage_CannotDeleteLocation_InUse&quot;: &quot;Ce lieu est associé à des plages de disponibilité ou à des rendez-vous à venir, il ne peut donc pas être retiré du profil de l&#8217;employé.&quot;,&quot;ErrorMessage_IncoherentTimeValues&quot;: &quot;L&#39;heure de fin doit être postérieure à celle du début.&quot;,&quot;Footer_Conditions_URL&quot;: &quot;/info/conditions.html&quot;,&quot;Footer_QuebecCopyRightGov_URL&quot;: &quot;https://www.quebec.ca/droit-auteur&quot;,&quot;Nav_Help_label&quot;: &quot;Aide&quot;,&quot;Nav_Help_URL&quot;: &quot;/info/aide.html&quot;,&quot;January_Short&quot;: &quot;jan.&quot;,&quot;February_Short&quot;: &quot;févr.&quot;,&quot;March_Short&quot;: &quot;mars&quot;,&quot;April_Short&quot;: &quot;avr.&quot;,&quot;May_Short&quot;: &quot;mai&quot;,&quot;June_Short&quot;: &quot;juin&quot;,&quot;July_Short&quot;: &quot;juill.&quot;,&quot;August_Short&quot;: &quot;août&quot;,&quot;September_Short&quot;: &quot;sept.&quot;,&quot;October_Short&quot;: &quot;oct.&quot;,&quot;November_Short&quot;: &quot;nov.&quot;,&quot;December_Short&quot;: &quot;déc.&quot;,&quot;ErrorMessage_Permissions_NoEstablishment&quot;: &quot;Vous devez sélectionner un établissement.&quot;,&quot;ErrorMessage_Permissions_NoEmployee&quot;: &quot;Vous devez sélectionner un employé.&quot;,&quot;ErrorMessage_Permissions_AlreadyExistsAll&quot;: &quot;L&#39;employé sélectionné a déjà des permissions définies pour tous les employés à tous les lieux.&quot;,&quot;ErrorMessage_Permissions_AlreadyExistsAllLocation&quot;: &quot;L&#39;employé sélectionné a déjà des permissions définies pour tous les employés du lieu {0}.&quot;,&quot;ErrorMessage_Permissions_AlreadyExists&quot;: &quot;Une permission est déjà définie pour {0} au lieu {1}.&quot;,&quot;Confirm_InactivateCompany&quot;: &quot;L&#8217;entreprise va être désactivée. Voulez-vous continuer?&quot;,&quot;ErrorMessage_NoSurveyConfigured&quot;: &quot;Vous n&#39;avez pas configuré de sondage. Veuillez consulter &lt;a href=&#39;http://RDVShelp.helpscoutdocs.com/article/109-send-a-survey&#39; target=&#39;_blank&#39;&gt;l&#39;aide&lt;/a&gt; pour voir comment le configurer.&quot;,&quot;ErrorMessage_ClientHasNoEmail&quot;: &quot;Ce client n&#39;a pas d&#39;adresse courriel&quot;,&quot;ErrorMessage_SaveDataBeforeExit&quot;: &quot;Veuillez enregistrer vos données avant de quitter.&quot;,&quot;ErrorMessage_SearchDateRangeTooBig&quot;: &quot;La période de date ne peut excéder 3 semaines. Nous l&#39;avons ajusté pour vous. Bienvenue!&quot;,&quot;ErrorMessage_UserMustHaveAStartDate&quot;: &quot;L&#39;employé doit avoir une date d&#39;entrée en fonction.&quot;,&quot;ErrorMessage_MandatoryFields&quot;: &quot;Veuillez remplir tous les champs obligatoires.&quot;,&quot;ErrorMessage_DateInFuture&quot;: &quot;La date de départ doit être postérieure à la date d&#39;entrée en fonction.&quot;,&quot;ErrorMessage_MissingDate&quot;: &quot;ErrorMessage_MissingDate&quot;,&quot;ErrorMessage_DateInvalid&quot;: &quot;Format de date invalide ou en dehors de la plage autorisée.&quot;,&quot;ErrorMessage_DateFuture&quot;: &quot;La date de fin doit être postérieure à la date de début.&quot;,&quot;ErrorMessage_Range_Higher_Nb_Months&quot;: &quot;Vous ne pouvez choisir une période de plus de 12 mois.&quot;,&quot;ErrorMessage_UserMustHaveARamqId&quot;: &quot;L&#39;utilisateur doit avoir un identifiant de la RAMQ.&quot;,&quot;ErrorMessage_UserMustHaveAPracticeNo&quot;: &quot;L&#39;utilisateur doit avoir un numéro de pratique.&quot;,&quot;ErrorMessage_UserMustHaveAtLeastOneWorkLocation&quot;: &quot;L&#39;employé doit être associé à au moins un lieu de travail.&quot;,&quot;ErrorMessage_ClinicMustHaveAGMFNo&quot;: &quot;Le numéro du GMF est obligatoire.&quot;,&quot;ErrorMessage_UserAlreadyExists&quot;: &quot;Cet employé existe déjà.&quot;,&quot;ErrorMessage_AvailabilitiesExist&quot;: &quot;Vous ne pouvez créer un horaire avec l&#8217;outil rapide de création dans une semaine qui contient déjà des plages de disponibilité.&quot;,&quot;Label_AppTitle&quot;: &quot;Rendez-vous santé Québec&quot;,&quot;Label_GouvQc&quot;: &quot;Gouvernement du Québec&quot;,&quot;Label_QcVisId&quot;: &quot;Identification visuelle du gouvernement du Québec&quot;,&quot;Label_IdClient&quot;: &quot;Identification de la personne assurée&quot;,&quot;Label_MetaDescription&quot;: &quot;Rendez-vous santé Québec, pour prendre rendez-vous avec un professionnel de la santé.&quot;,&quot;Label_Identification_RDV&quot;: &quot;Rendez-vous santé Québec&quot;,&quot;Label_Confirmation_RDV&quot;: &quot;Confirmation - Rendez-vous santé Québec&quot;,&quot;ErrorMessage_FillAllFields&quot;: &quot;Veuillez remplir tous les champs.&quot;,&quot;ErrorMessage_InvalideDateformat&quot;: &quot;La date saisie est invalide.&quot;,&quot;WarningMessage_NoMd&quot;: &quot;Selon nos renseignements, vous n&#39;avez pas de médecin de famille. Vous pouvez vous inscrire sur la liste d&#39;attente par le biais du &lt;a href=&#39;http://www.gamf.gouv.qc.ca/&#39; target=&#39;_blank&#39;&gt;Guichet d&#39;accès à un médecin de famille&amp;nbsp;&lt;img src=&#39;/images/lien_externe.gif&#39; alt=&#39;Cet hyperlien s&amp;#8217;ouvrira dans une nouvelle fenêtre.&#39; width=&#39;9&#39; height=&#39;9&#39;&gt;&lt;/a&gt;. Vous pouvez aussi prendre rendez-vous avec un professionnel de la santé dans une clinique à proximité. &quot;,&quot;PresentCamAtTheAppointment&quot;: &quot;Vous devez présenter votre carte d&#39;assurance maladie valide au moment du rendez-vous, sinon la clinique pourrait refuser votre consultation ou vous imposer des frais.&quot;,&quot;WarningMessage_ExpiredNAM&quot;: &quot;Nos dossiers indiquent que votre situation doit être régularisée auprès de la RAMQ. Communiquez avec nous au **************. Nos bureaux sont ouverts de 8:30 à 16:30 le lundi, le mardi, le jeudi et le vendredi, et de 10:00 à 16:30 le mercredi. Vous pouvez quand même prendre rendez-vous à l&#8217;aide du service en ligne, mais la clinique se réserve le droit de refuser ou de vous facturer votre consultation si votre situation n&#8217;a pas changé au moment de votre rendez-vous.&quot;,&quot;ErrorMessage_ServicesAccessDenied&quot;: &quot;Les informations fournies ne correspondent à aucun dossier à la RAMQ. Veuillez les vérifier. Si la situation persiste, vous pouvez communiquer avec nous au **************. Nos bureaux sont ouverts de 8:30 à 16:30 le lundi, mardi, jeudi et vendredi, et de 10:00 à 16:30 le mercredi. En cas d&#39;urgence, vous pouvez téléphoner à votre clinique pour prendre rendez-vous ou consulter le &lt;a href=&#39;http://sante.gouv.qc.ca/repertoire-ressources/consultations-medicales-sans-rendez-vous/&#39; target=&#39;_blank&#39;&gt;Portail santé mieux-être &lt;/a&gt; pour voir l&#39;horaire des cliniques qui offrent un service de consultation sans rendez-vous.&quot;,&quot;Label_AssureAppointmentMD&quot;: &quot;Prendre rendez-vous avec mon professionnel de la santé&quot;,&quot;Label_AssureAppointmentGMF&quot;: &quot;Prendre rendez-vous avec un membre de l&#39;équipe de relais n&lt;sup&gt;o&lt;/sup&gt; 1 de mon professionnel de la santé&quot;,&quot;Label_AssureAppointmentNearbyClinic&quot;: &quot;Prendre rendez-vous dans une clinique à proximité&quot;,&quot;Label_AssureAppointmentModifyCancel&quot;: &quot;Mettre à jour mes coordonnées ou annuler mon rendez-vous&quot;,&quot;ErrorMessage_NoAppointmentOnLine&quot;: &quot;Votre professionnel de la santé n&#39;a aucune plage de rendez-vous disponible en ligne. Veuillez communiquer avec votre clinique pour connaître ses disponibilités.&quot;,&quot;WarningMessage_InscrFollowLocEnding&quot;: &quot;Selon nos informations, à compter du {0}, des changements sont prévus à votre clinique. Pour plus de détails, veuillez communiquer avec celle-ci.&quot;,&quot;WarningMessage_InscrGMFEnding&quot;: &quot;Selon nos informations, à compter du {0}, des changements sont prévus à votre clinique. Pour plus de détails, veuillez communiquer avec celle-ci.&quot;,&quot;WarningMessage_RelationMDEnding&quot;: &quot;Selon nos informations, vous n&#8217;aurez plus de professionnel de la santé à compter du {0}. Pour plus de détails, veuillez communiquer avec votre clinique.&quot;,&quot;ErrorMessage_NoMoreResult&quot;: &quot;Toutes les plages de disponibilité de votre professionnel de la santé offertes en ligne ont été comblées.&quot;,&quot;ErrorMessage_ResultNotFound&quot;: &quot;Aucun rendez-vous répondant à vos critères de recherche n&#39;est disponible pour le moment.&quot;,&quot;ErrorMessage_ResultNotFoundCovid19&quot;: &quot;&lt;strong&gt;Si vous devez prendre un rendez-vous de dépistage de la COVID-19&lt;/strong&gt;, téléphonez au **************.&quot;,&quot;ErrorMessage_ResultNotFoundLienSante&quot;: &quot;&lt;strong&gt;Pour connaître les ressources qui offrent de la consultation médicale le jour même ou le lendemain aux personnes sans médecin de famille ou dont le médecin n&#8217;est pas disponible, consultez la page &lt;/strong&gt; &lt;a href=&#39;http://sante.gouv.qc.ca/repertoire-ressources/consultations-medicales-sans-rendez-vous/&#39;&gt;Trouver une ressource qui offre de la consultation médicale le jour même ou le lendemain&lt;/a&gt;.&quot;,&quot;ErrorMessage_NoAvailability&quot;: &quot;Aucun rendez-vous n&#39;est disponible.&quot;,&quot;ErrorMessage_ResultTimeout&quot;: &quot;La limite de temps de recherche est dépassée. Veuillez réessayer plus tard ou communiquer directement avec votre clinique.&quot;,&quot;ErroMessage_PerimeterOffLimits&quot;: &quot;Le périmètre de recherche doit être compris entre 1 et 100 kilomètres. Veuillez en choisir un nouveau.&quot;,&quot;Label_MinusButtonDesc&quot;: &quot;Le périmètre de recherche doit être compris entre 1 et 100 kilomètres. Veuillez en choisir un nouveau.&quot;,&quot;Label_PlusButtonDesc&quot;: &quot;Le périmètre de recherche doit être compris entre 1 et 100 kilomètres. Veuillez en choisir un nouveau.&quot;,&quot;Label_InformationNotAvailable&quot;: &quot;Vous n&#8217;avez pas saisi l&#8217;information.&quot;,&quot;Label_AppointmentIsAvailable&quot;: &quot;Le rendez-vous de {0} est disponible.&quot;,&quot;Message_email_non_attendance_body&quot;: &quot;    &lt;p&gt;Bonjour [prénom et nom du patient],&lt;/p&gt;    &lt;p&gt;Vous avez manqué votre rendez-vous médical en clinique prévu le &lt;b&gt;[Date rendez-vous], à [Heure rendez-vous]&lt;/b&gt;, dans l&#8217;établissement suivant : &lt;b&gt;[Nom du lieu]&lt;/b&gt;. Notez que la clinique se réserve le droit de vous imposer des frais pour cette absence.&lt;/p&gt;    &lt;p&gt;La prochaine fois, pensez à annuler votre rendez-vous pour que nous puissions offrir la plage horaire à un autre patient.&lt;/p&gt;    &lt;p&gt;Merci d&#8217;utiliser Rendez-vous santé Québec et bonne journée!&lt;/p&gt;    &quot;,&quot;Date_Format&quot;: &quot;dd-MM-yyyy&quot;,&quot;Label_Date_Format&quot;: &quot;jj-mm-aaaa&quot;,&quot;Date_Format_Datepicker&quot;: &quot;dd-mm-yyyy&quot;,&quot;Label_BirthdayDayAssure&quot;: &quot;Date de naissance (jour)&quot;,&quot;Label_BirthdayMonthAssure&quot;: &quot;Date de naissance (mois)&quot;,&quot;Label_BirthdayYearAssure&quot;: &quot;Date de naissance (année)&quot;,&quot;Message_NoReloadNeeded&quot;: &quot;L&#39;information de cette page peut être mise à jour sans rechargement.&quot;,&quot;Label_AvailableAppointments&quot;: &quot;Rendez-vous disponibles pour « {0} » à partir du&quot;,&quot;Label_determinant_de&quot;: &quot;de&quot;,&quot;Label_determinant_d&quot;: &quot;d&#39;&quot;,&quot;Label_Title_Kind_Doctor&quot;: &quot;Médecin de famille&quot;,&quot;Label_Title_Kind_IPS&quot;: &quot;Infirmier ou infirmière praticienne spécialisée&quot;,&quot;Label_Title_Kind_Inconnu&quot;: &quot;Non disponible&quot;,&quot;Label_Title_Kind_IPS_QuickSearch&quot;: &quot;IPS&quot;,&quot;Message_Loading&quot;: &quot;Chargement ...&quot;,&quot;ErrorMessage_PleaseUseDifferentEmailToTestAsClient&quot;: &quot;Hum, on dirait que vous voulez essayer la prise de rendez-vous en tant que client.&lt;br/&gt;Veuillez utiliser une adresse courriel différente de votre adresse courriel en tant que professionnel, sinon, le système pensera que vous avez pris un rendez-vous avec vous-même et vous ne recevrez pas de notifications.&quot;,&quot;ErrorMessage_EnableBrowserCookiesToContinue&quot;: &quot;Pour continuer, veuillez activer les témoins (cookies) sur votre navigateur.&quot;,&quot;ErrorMessage_LoginInvalid&quot;: &quot;L&#39;identifiant ou le mot de passe saisi est invalide. Veuillez consulter la &lt;a href=&#39;https://rvsq.gouv.qc.ca/info/aide-prof.html&#39; target=&#39;_blank&#39;&gt;section Aide&lt;/a&gt;.&quot;,&quot;Label_Done&quot;: &quot;Complété&quot;,&quot;Label_AllProfessionals&quot;: &quot;Tous les professionnels&quot;,&quot;Label_Rooms&quot;: &quot;Salles&quot;,&quot;Label_Employees&quot;: &quot;Faire une sélection&quot;,&quot;Label_Equipment&quot;: &quot;Équipements&quot;,&quot;Label_SmsFees&quot;: &quot;Des frais standards pour les SMS peuvent s&#39;appliquer. Vérifiez les modalités de votre abonnement auprès de votre fournisseur.&quot;,&quot;Label_SearchingAvailabilities&quot;: &quot;Recherche d&#39;heures disponibles&quot;,&quot;Label_DurationValueHoursMinutes&quot;: &quot;{0} heure{2} et {1} minute{3}&quot;,&quot;Label_DurationValueHours&quot;: &quot;{0} heure{2}&quot;,&quot;Label_DurationValueMinutes&quot;: &quot;{1} minute{3}&quot;,&quot;Column_Email&quot;: &quot;Courriel&quot;,&quot;Column_PreparationTime&quot;: &quot;Préparation&quot;,&quot;Column_Address&quot;: &quot;Coordonnées&quot;,&quot;Column_Type&quot;: &quot;Type&quot;,&quot;Column_Status&quot;: &quot;Statut&quot;,&quot;Column_Duration&quot;: &quot;Durée&quot;,&quot;English&quot;: &quot;Anglais&quot;,&quot;French&quot;: &quot;Français&quot;,&quot;Warning_IncompleteAvailabilitiesWillBeCopied&quot;: &quot;L&#39;horaire que vous êtes sur le point de reproduire contient des plages de disponibilité incomplètes.&quot;,&quot;Error_CancelLimit&quot;: &quot;Le délai maximum pour l&#8217;annulation en ligne doit se situer entre 0 et 59 minutes ou entre 0 et 24 heures.&quot;,&quot;Label_NewEmployee&quot;: &quot;Nouvel employé&quot;,&quot;Button_WorkSchedule&quot;: &quot;Horaire&quot;,&quot;Message_QuickStartTip&quot;: &quot;Cliquez sur ces 4 étapes pour configurer l&#39;essentiel et acceptez des rendez-vous en ligne dès aujourd&#39;hui!&quot;,&quot;Message_FeatureNotAvailableInFreePlan&quot;: &quot;Cette fonctionalité n&#39;est pas incluse avec le forfait gratuit. Veuillez vous &lt;a href=&#39;../pricing/&#39;&gt;abonner&lt;/a&gt; au plan professionnel pour l&#39;utiliser.&quot;,&quot;Message_MustCallCompanyForFirstAppointment&quot;: &quot;Veuillez appeler au {0} pour demander votre premier rendez-vous chez {1}.&quot;,&quot;Message_MustCallProfessionalForFirstAppointment&quot;: &quot;Veuillez appeler au {0} pour demander votre premier rendez-vous avec {1}.&quot;,&quot;Message_MustEmailCompanyForFirstAppointment&quot;: &quot;Veuillez envoyer un courriel à &lt;a href=&#39;mailto:{0}&#39; target=&#39;_blank&#39;&gt;{0}&lt;/a&gt; pour demander votre premier rendez-vous chez {1}.&quot;,&quot;Message_MustEmailProfessionalForFirstAppointment&quot;: &quot;Veuillez envoyer un courriel à &lt;a href=&#39;mailto:{0}&#39; target=&#39;_blank&#39;&gt;{0}&lt;/a&gt; pour demander votre premier rendez-vous avec {1}.&quot;,&quot;Placeholder_PleaseCallToBookFirstAppointmentCustomMessage&quot;: &quot;Si vous laissez ce champs vide, nous leur dirons : {0}&quot;,&quot;Message_PleaseCallToCancelThisAppointment&quot;: &quot;Veuillez appeler votre professionnel si vous devez annuler ce rendez-vous.&quot;,&quot;Message_MustCallCompanyForAppointment&quot;: &quot;Veuillez appeler au {0} pour demander un rendez-vous chez {1}.&quot;,&quot;Message_MustCallProfessionalForAppointment&quot;: &quot;Veuillez appeler au {0} pour demander un rendez-vous avec {1}.&quot;,&quot;Message_MustEmailCompanyForAppointment&quot;: &quot;Veuillez envoyer un courriel à &lt;a href=&#39;mailto:{0}&#39; target=&#39;_blank&#39;&gt;{0}&lt;/a&gt; pour demander un rendez-vous chez {1}.&quot;,&quot;Message_MustEmailProfessionalForAppointment&quot;: &quot;Veuillez envoyer un courriel à &lt;a href=&#39;mailto:{0}&#39; target=&#39;_blank&#39;&gt;{0}&lt;/a&gt; pour demander un rendez-vous avec {1}.&quot;,&quot;Label_BornOnDate&quot;: &quot;Né le {0}&quot;,&quot;Message_ProfessionalNoAvailabilitiesTextMobile&quot;: &quot;Désolé, il n&#39;y a aucune disponibilité le &lt;strong&gt;{0}&lt;/strong&gt;&quot;,&quot;Button_SeeSoonestWeekWithAvailabilities&quot;: &quot;Trouvons le prochain rendez-vous disponible&quot;,&quot;Label_NoPrimaryProfessional&quot;: &quot;Aucun professionnel&quot;,&quot;Label_ActivityClientNotes&quot;: &quot;Il y a des notes sur le client ou le rendez-vous. Voir l&#39;onglet Notes.&quot;,&quot;Button_Menu&quot;: &quot;Menu&quot;,&quot;Button_Edit&quot;: &quot;Modifier&quot;,&quot;Button_Save&quot;: &quot;Enregistrer&quot;,&quot;Button_BookAppointment&quot;: &quot;Prendre un rendez-vous&quot;,&quot;Button_ViewMap&quot;: &quot;Voir sur la carte&quot;,&quot;Button_CancelAppointment&quot;: &quot;Annuler mon rendez-vous&quot;,&quot;Button_ChangeAppointment&quot;: &quot;Modifier le rendez-vous&quot;,&quot;Error_TAhoursModal&quot;: &quot;Votre modèle contient des heures en dehors des heures affichées.&quot;,&quot;ErrorMessage_InvalidClientAvailability&quot;: &quot;Veuillez choisir un jour et une plage de rendez-vous valides.&quot;,&quot;ErrorMessage_ContactUsEmptyFields&quot;: &quot;Veuillez entrer votre nom et votre adresse courriel avec votre message.&quot;,&quot;ErrorMessage_InvalidTime&quot;: &quot;L&#39;heure ou la date saisie ne correspond à aucune plage de disponibilité.&quot;,&quot;ErrorMessage_AjaxError&quot;: &quot;Une erreur s&#39;est produite. Veuillez vérifier votre connexion Internet.&quot;,&quot;ErrorMessage_GenderMustBeSelected&quot;: &quot;Veuillez choisir le sexe.&quot;,&quot;ErrorMessage_UserMustHaveAName&quot;: &quot;L&#39;utilisateur doit avoir un prénom et un nom&quot;,&quot;ErrorMessage_SessionExpired&quot;: &quot;Votre session est expirée. Vous serez redirigé vers la page de connexion&quot;,&quot;ErrorMessage_SessionExpired_Client&quot;: &quot;Votre session est expirée. Vous serez redirigé vers la page d&#39;identification.&quot;,&quot;ErrorMessage_PhoneNumberIsInvalid&quot;: &quot;Le numéro de téléphone saisi est invalide.&quot;,&quot;ErrorMessage_NameIsMandatory&quot;: &quot;Vous devez obligatoirement entrer un nom.&quot;,&quot;ErrorMessage_InvalidAvailabilityTime&quot;: &quot;Les heures de disponibilité doivent débuter à des intervalles de 5 minutes. Ex: 13:00, 13:05, 13:15 ou 13:30&quot;,&quot;ErrorMessage_EmailCanOnlyBeChanedByOwner&quot;: &quot;L&#39;adresse courriel peut seulement être modifiée par le propriétaire du compte&quot;,&quot;ErrorMessage_EmailIsMandatory&quot;: &quot;L&#39;adresse courriel est obligatoire.&quot;,&quot;ErrorMessage_LabelIsMandatory&quot;: &quot;Le nom du lieu est obligatoire.&quot;,&quot;ErrorMessage_TerritoryIsMandatory&quot;: &quot;Le code de localité est obligatoire.&quot;,&quot;ErrorMessage_StartDate&quot;: &quot;La date de début est obligatoire.&quot;,&quot;ErrorMessage_StreetNumber&quot;: &quot;Le numéro de rue est obligatoire.&quot;,&quot;ErrorMessage_RoadNumber&quot;: &quot;Le nom de la rue est obligatoire.&quot;,&quot;ErrorMessage_Appart&quot;: &quot;Le bureau est obligatoire&quot;,&quot;ErrorMessage_PostalCode&quot;: &quot;Le code postal est obligatoire.&quot;,&quot;ErrorMessage_City&quot;: &quot;La ville est obligatoire.&quot;,&quot;ErrorMessage_Province&quot;: &quot;La province est obligatoire.&quot;,&quot;ErrorMessage_PhoneNumber&quot;: &quot;Le numéro de téléphone est obligatoire.&quot;,&quot;ErrorMessage_CannotDeleteLocation&quot;: &quot;ErrorMessage_CannotDeleteLocation&quot;,&quot;ErrorMessage_NoRAMQIdProvided&quot;: &quot;Veuillez saisir un identifiant RAMQ.&quot;,&quot;ErrorMessage_ProfessionalNotFound&quot;: &quot;Cet employé n&#8217;est pas inscrit au registre des services en ligne de la RAMQ.&quot;,&quot;ErrorMessage_RamqIdNotFound&quot;: &quot;Cet identifiant ne correspond à aucun utilisateur connu.&quot;,&quot;Message_InactivateCompany&quot;: &quot;L&#39;entreprise a été désactivée.&quot;,&quot;company_not_gmf&quot;: &quot;Entreprise non GMF&quot;,&quot;ErrorMessage_UnknowCountryCode&quot;: &quot;Le code pays que vous avez saisi, {0}, nous est inconnu. La première boite du numéro de téléphone est pour le code du pays. Ex: 1 pour le Canada et US, 33 pour la France.&quot;,&quot;ErrorMessage_InvalidBirthday&quot;: &quot;The date of birth entered is invalid.&quot;,&quot;ErrorMessage_CannotSaveOverlappingAvailabilities&quot;: &quot;Vous ne pouvez pas créer une plage de disponibilité qui en chevauche une autre.&quot;,&quot;ErrorMessage_ClinicMustHaveAName&quot;: &quot;Le nom de l&#39;entreprise est obligatoire.&quot;,&quot;ErrorMessage_CompanyUser&quot;: &quot;ErrorMessage_CompanyUser&quot;,&quot;ErrorMessage_ClinicMustHaveAPhoneNumber&quot;: &quot;Le numéro de téléphone de l&#39;entreprise est obligatoire.&quot;,&quot;ErrorMessage_Longitude&quot;: &quot;La longitude est obligatoire.&quot;,&quot;ErrorMessage_Latitude&quot;: &quot;La latitude est obligatoire.&quot;,&quot;Message_CountryCodeIsMandatory&quot;: &quot;Le code de pays est obligatoire&quot;,&quot;Message_FreeAccountSurveyLimit&quot;: &quot;Avec un compte gratuit vous pouvez envoyer jusqu&#39;à 15 sondages. Il reste {0} sondages. &lt;a href=&#39;javascript:;&#39; class=&#39;h-SubscribeButton&#39;&gt;Adhérez à un plan professionnel&lt;/a&gt; maintenant pour avoir un nombre illimité de sondages.&quot;,&quot;Label_GlobalList&quot;: &quot;Liste globale&quot;,&quot;ErrorMessage_ExclusiveMinutesCannotBeLongerThenDuration&quot;: &quot;Le délai de temps exclusif ne peut pas être plus long que la durée du service.&quot;,&quot;Message_AppointmentRequestSent&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;Votre demande de rendez-vous a été envoyée à {0}. Vous devriez recevoir une réponse par courriel sous peu.&lt;/p&gt;&lt;p&gt;Ce que vous regardez est &lt;strong&gt;votre propre agenda gratuit.&lt;/strong&gt;&lt;/p&gt;&quot;,&quot;Message_AppointmentWithProfessionalBooked&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;Votre prise de rendez-vous avec {0} est complétée.&lt;/p&gt;&lt;p&gt;Ce que vous regardez est &lt;strong&gt;votre propre agenda gratuit.&lt;/strong&gt;&lt;/p&gt;&quot;,&quot;Title_CongratsExclamation&quot;: &quot;Félicitations!&quot;,&quot;Label_HelpSettingAlwaysShowThisMessage&quot;: &quot;Toujours afficher ce message&quot;,&quot;Message_CalendarNavigationHelp&quot;: &quot;Saviez-vous que vous pouvez rapidement naviguer à une semaine en cliquant ici et en choisissant une date dans le calendrier?&quot;,&quot;Message_ScheduleClienFromDetailsHelp&quot;: &quot;Vous pouvez créer un rendez-vous avec un client en glissant-déposant le client sur votre agenda avec cette icône.&quot;,&quot;Message_DataAccessRequestSent&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;Ce client utilise déjà RDVS!&lt;/p&gt;&lt;p class=&#39;Interligne&#39;&gt;Un courriel lui a été envoyé pour demander accès à ses informations personnelles.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Vous pouvez prendre rendez-vous avec dès maintenant&lt;/strong&gt; et toutes les notifications par courriels et par messages textes lui seront envoyées.&lt;/p&gt;&quot;,&quot;Message_DataAccessRequestResent&quot;: &quot;Nous avons envoyé un nouveau courriel au client pour lui demander son authorisation d&#39;accéder à ses informations.&quot;,&quot;Message_Tentative_Bloque&quot;: &quot;&lt;b&gt;Accès temporairement bloqué&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;Vous avez atteint le nombre maximal de tentatives de connexion. Votre accès à Rendez-vous santé Québec est bloqué pour les prochaines 24 heures.&lt;br/&gt;&lt;br/&gt;Pour prendre rendez-vous dans une clinique médicale, veuillez contacter directement votre clinique ou appeler le 811.&quot;,&quot;ErrorMessage_YouAlreadyHaveAClientWithThisEmail&quot;: &quot;Cette adresse courriel est associée à un de vos clients.&quot;,&quot;ErrorMessage_YouAlreadyHaveAClientWithThisName&quot;: &quot;Vous avez déjà un client avec ce nom. Que voulez-vous faire?&quot;,&quot;Button_Send&quot;: &quot;Envoyer&quot;,&quot;Button_View&quot;: &quot;Voir&quot;,&quot;Button_Continue&quot;: &quot;Continuer&quot;,&quot;Button_Close&quot;: &quot;Fermer&quot;,&quot;Button_CreateNewClient&quot;: &quot;Créer un nouveau client&quot;,&quot;Button_ViewExistingClient&quot;: &quot;Voir le client existant&quot;,&quot;Title_Attention&quot;: &quot;Attention&quot;,&quot;Label_Sent&quot;: &quot;Envoyé!&quot;,&quot;Button_GotIt&quot;: &quot;J&#39;ai compris&quot;,&quot;Label_AnyProfessionalAtCompany&quot;: &quot;N&#39;importe quel professionnel&quot;,&quot;Message_Confirmation_YouCanCloseTab&quot;: &quot;Merci d&#8217;avoir utilisé ce service. Vous pouvez fermer cette fenêtre.&quot;,&quot;DeleteTimeAvailabilities_For&quot;: &quot;Supprimer des disponibilités -&quot;,&quot;Content_NavigationInterceptor_DeleteMode&quot;: &quot;Pour quitter le mode de suppression des disponibilités, vous devez sélectionner « J&#8217;ai terminé ».&quot;,&quot;Label_NabigationInterceptorTitle_EditSchedule_DeleteMode&quot;: &quot;Modifications non enregistrées&quot;,&quot;Button_TryAgain&quot;: &quot;Réessayer&quot;,&quot;Info_LimitedEmployeeValues&quot;: &quot;Seuls les professionnels de la santé auxquels vous avez accès sont affichés dans cette liste.&quot;,&quot;Info_LimitedSiteValues&quot;: &quot;Seuls les lieux auxquels vous avez accès sont affichés dans cette liste.&quot;,&quot;Info_ClientFilters&quot;: &quot;Si vous ne saisissez aucune information, la liste sera produite pour tous les patients correspondant aux autres critères.&quot;,&quot;ReportIssue_DescribeIssue&quot;: &quot;Veuillez décrire le problème que vous rencontrez&quot;,&quot;ReportIssue_EditScreenshot&quot;: &quot;Veuillez dessiner un rond ou une flèche pour indiquer où est le problème dans cette capture d&#39;écran&quot;,&quot;Label_IssueSent&quot;: &quot;Merci d&#39;avoir rapporté le problème. Notre équipe de support le regardera rapidement.&quot;,&quot;Message_CreateWorkScheduleHelp&quot;: &quot;&lt;p&gt;Pour créer votre horaire de travail, veuillez cliquer dans votre horaire pour ajouter des périodes de disponibilités. Les périodes ont une durée de 4 heures par défaut, mais vous pouvez ajuster la durée comme vous voulez.&lt;/p&gt;&lt;p&gt;Sous le menu &lt;strong&gt;Actions&lt;/strong&gt;, vous trouverez des outils pour vous aider à gérer votre horaire.&lt;p/&gt;&quot;,&quot;Message_InvisibleWorkScheduleAvailabilities&quot;: &quot;Votre horaire de travail contient des plages de disponibilité non visibles ou partiellement visibles. Veuillez le modifier pour éviter que des patients prennent rendez-vous à des moments inattendus.&quot;,&quot;Label_Tip&quot;: &quot;Aide&quot;,&quot;Tooltip_DeleteActivity&quot;: &quot;Annuler le rendez-vous&quot;,&quot;centreHospitalierActivityTag&quot;: &quot;(réorientation)&quot;,&quot;centreHospitalier_button&quot;: &quot;Réorienter un patient d&#8217;un hôpital vers une clinique &quot;,&quot;int911_button&quot;: &quot;Réorienter un appelant au 911 vers une clinique&quot;,&quot;intGap_button&quot;: &quot;Guichet d&#39;accès aux services de première ligne&quot;,&quot;intervenantClinique_button&quot;: &quot;Réorienter un patient d&#8217;une clinique vers une autre&quot;,&quot;quickSearch_main_title_redirection&quot;: &quot;Centre hospitalier&quot;,&quot;quickSearch_main_title_redirection_clinique&quot;: &quot;Réorientation d&#39;un patient vers une autre clinique&quot;,&quot;quickSearch_main_title_redirection_911&quot;: &quot;911&quot;,&quot;quickSearch_main_title_redirection_GAP&quot;: &quot;quickSearch_main_title_redirection_GAP&quot;,&quot;Message_MaxStaffForFreeAccount&quot;: &quot;Le forfait gratuit ne permet pas de gérer l&#39;horaire de plusieurs professionnels. Veuillez vous abonner au forfait professionnel pour avoir accès à cette fonctionnalité.&quot;,&quot;Message_MaxStaffForProAccount&quot;: &quot;Votre forfait ne permet pas de gérer plus de {0} professionnels. Veuillez nous contacter au 1.866.463.8381 si vous avez besoin de gérer plus de {0} professionnels.&quot;,&quot;Button_BookProfessional&quot;: &quot;Prendre un rendez-vous&quot;,&quot;Label_Suite&quot;: &quot;Bureau&quot;,&quot;Message_QuickBookStep1&quot;: &quot;Choisir un professionnel&quot;,&quot;Message_QuickBookStep2&quot;: &quot;Choisir un service&quot;,&quot;DeleteTimeAvailabilities_Confirmation&quot;: &quot;Voulez-vous vraiment supprimer ces disponibilités? Vous ne pourrez pas les récupérer.&quot;,&quot;Label_Hours&quot;: &quot;Heure(s)&quot;,&quot;Label_Minutes&quot;: &quot;Minute(s)&quot;,&quot;Tooltip_UpdateWorkSchedule&quot;: &quot;Modifier l&#39;horaire de travail. L&#39;horaire de travail est affiché avec des cases bleues&quot;,&quot;Tooltip_Update&quot;: &quot;Modifier&quot;,&quot;Tooltip_PrintSchedule&quot;: &quot;Imprimer l&#39;horaire&quot;,&quot;Tooltip_PrintRevenueReport&quot;: &quot;Imprimer le rapport de revenus&quot;,&quot;ToolTip_PublicAvailability&quot;: &quot;Plage publique&quot;,&quot;ToolTip_ReadOnly&quot;: &quot;Lecture seulement&quot;,&quot;ToolTip_ReadWrite&quot;: &quot;Lecture et écriture&quot;,&quot;Label_ActivityWithSelfOnly&quot;: &quot;Cet événement n&#39;est PAS avec un professionnel&quot;,&quot;Label_FilterLabelResources&quot;: &quot;{0} à {1} de {2}&quot;,&quot;Label_FilterLabelResourcesSingleDisplay&quot;: &quot;{0} de {2}&quot;,&quot;Label_SideBySide&quot;: &quot;Afficher les horaires côte à côte&quot;,&quot;quickSearch_Perimeter_Title&quot;: &quot;Périmètre &quot;,&quot;quickSearch_DOB_Label&quot;: &quot;DATE DE NAISSANCE&quot;,&quot;quickSearch_PostalCode_Label&quot;: &quot;CODE POSTAL&quot;,&quot;quickSearch_ProSante_Label&quot;: &quot;Professionnel attitré&quot;,&quot;quickSearch_LieuSuivi_Label&quot;: &quot;Lieu de suivi habituel&quot;,&quot;quickSearch_Inconnu_Label&quot;: &quot;Inconnu&quot;,&quot;quickSearch_ProSanteEmpty_Label&quot;: &quot;Aucun&quot;,&quot;quickSearch_RLS_Label&quot;: &quot;Réseau local de services&quot;,&quot;Label_obtainConsultationWithHealthProfessional_title&quot;: &quot;Obtenir une consultation avec un professionnel de la santé&quot;,&quot;Label_obtainConsultationWithHealthProfessional_text&quot;: &quot;Comme vous êtes inscrit auprès d&#8217;un groupe de médecins, vous pouvez obtenir une consultation ou un service médical en utilisant le&quot;,&quot;Label_obtainConsultationWithHealthProfessional_link&quot;: &quot;Guichet d&#8217;accès à la première ligne (GAP)&quot;,&quot;Link_GAP&quot;: &quot;https://www.quebec.ca/sante/trouver-une-ressource/guichet-acces-premiere-ligne&quot;,&quot;Help_SearchUser&quot;: &quot;Filtrez votre liste de clients en entrant une partie du prénom, du nom ou de l&#39;adresse courriel. Ceci vous permet de trouver rapidement une fiche client.&quot;,&quot;Label_HeresATrick&quot;: &quot;Voici une astuce&quot;,&quot;Label_Sent&quot;: &quot;Envoyé!&quot;,&quot;Message_ReminderSent&quot;: &quot;Le rappel a été envoyé. Nous n&#39;enverrons pas d&#39;autres rappels pour ce rendez-vous.&quot;,&quot;Message_ReminderAlreadySent&quot;: &quot;Le rappel pour ce rendez-vous a déjà été envoyé à votre client.&quot;,&quot;GettingStarted_ThisIsHelpButtonMessage&quot;: &quot;Ce bouton est ici pour vous montrer à quoi il ressemble pour le repérer dans l&#39;interface. Vous devez fermer cette fenêtre et cliquer sur le vrai bouton.&quot;,&quot;Message_ConfirmCancelAppointment&quot;: &quot;Voulez-vous vraiment annuler ce rendez-vous?&quot;,&quot;Message_ConfirmDeleteAddress&quot;: &quot;Les données vont être supprimées. Voulez-vous continuer?&quot;,&quot;Message_ConfirmDeleteModal&quot;: &quot;Voulez-vous vraiment supprimer ce modèle d&#39;horaire? Vous ne pourrez pas le récupérer.&quot;,&quot;Label_NavigationInterceptorBody_Template&quot;: &quot;Veuillez enregistrer vos données avant de quitter&quot;,&quot;Message_ConfirmDeleteDLO&quot;: &quot;Les données vont être supprimées. Voulez-vous continuer?&quot;,&quot;Message_ConfirmDeleteEmployee&quot;: &quot;Êtes-vous certains de vouloir supprimer cet employé?&quot;,&quot;Message_Confirm&quot;: &quot;Êtes-vous certain?&quot;,&quot;Message_SignatureUploadedSuccessfully&quot;: &quot;Génial! Cette signature sera maintenant ajoutée aux reçus que vous imprimez ou envoyez par courriel à vos clients.&quot;,&quot;Message_CopyServicesFromThisProfessional&quot;: &quot;Copier les services de ce professionnel&quot;,&quot;Tooltip_CopyServicesFromAnotherProfessional&quot;: &quot;Copier les services d&#39;un autre professionnel&quot;,&quot;Button_Copy&quot;: &quot;Appliquer&quot;,&quot;Button_Done&quot;: &quot;J&#39;ai terminé.&quot;,&quot;Message_UsingDefaultSchedule&quot;: &quot;Cette semaine utilise votre horaire de travail récurrent par défaut&quot;,&quot;Message_AllTimeAvailabilitiesDeletedUsingDefaultSchedule&quot;: &quot;&quot;,&quot;Message_ApplyingDefaultScheduleConfirmation&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;L&#39;horaire de travail affiché &lt;strong&gt;sera remplacé par votre horaire récurrent par défaut&lt;/strong&gt;.&lt;/p&gt;&lt;p&gt;Êtes-vous sûr de vouloir continuer?&lt;/p&gt;&quot;,&quot;Message_DeleteWeekScheduleConfirmation&quot;: &quot;L&#39;horaire de travail de cette semaine sera supprimé. Voulez-vous quand même continuer?&quot;,&quot;Message_DeleteDefaultScheduleConfirmation&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;L&#39;horaire de travail récurrent par défaut &lt;strong&gt;sera supprimé&lt;/strong&gt;.&lt;/p&gt;&lt;p&gt;Êtes-vous sûr de vouloir continuer?&lt;/p&gt;&quot;,&quot;Message_UseAsDefaultScheduleConfirmation&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;L&#39;horaire de travail affiché &lt;strong&gt;remplacera votre horaire récurrent par défaut&lt;/strong&gt;.&lt;/p&gt;&lt;p&gt;Êtes-vous sûr de vouloir continuer?&lt;/p&gt;&quot;,&quot;Button_UpdateWeekWorkScheduleButton&quot;: &quot;Modifier mon horaire de travail&quot;,&quot;Button_UpdateDefaultWorkScheduleButton&quot;: &quot;Modifier mon horaire de travail récurrent&quot;,&quot;Label_CopyWorkScheduleTo&quot;: &quot;Appliquer cet horaire à d&#39;autres semaines&quot;,&quot;DataTable_permissionEmpty&quot;: &quot;Aucune dérogation&quot;,&quot;DataTable_permissionEmptyFound&quot;: &quot;Aucune dérogation trouvée&quot;,&quot;DataTable_groupEmptyFound&quot;: &quot;Aucun groupe trouvé&quot;,&quot;DataTable_sZeroRecords2&quot;: &quot;Aucun résultat&quot;,&quot;error_message_professionalGroup_not_unique&quot;: &quot;Ce groupe de travail existe déjà.&quot;,&quot;Label_Help&quot;: &quot;Aide&quot;,&quot;Button_UpdateEmployeeSettings&quot;: &quot;Modifier les propriétés (Informations, services, reçus, etc.)&quot;,&quot;Label_CopyWeekSchedule&quot;: &quot;Appliquer aux {0} prochaines semaines, à toutes les {1} semaines, {2}.&quot;,&quot;Label_CopyWeekSchedule_WeekRange&quot;: &quot;du {0} au {1}&quot;,&quot;DeleteTimeAvailabilities_Button&quot;: &quot;DeleteTimeAvailabilities_Button&quot;,&quot;Title_WorkSchedule&quot;: &quot;Horaire de travail&quot;,&quot;Message_MaxAddressesForFreeAccount&quot;: &quot;Le forfait gratuit ne permet pas d&#39;ajouter plus d&#39;une adresse à votre entreprise.&quot;,&quot;Message_ClientMustCallForFirstAppointment&quot;: &quot;Veuillez appeler {0} au {1} pour prendre votre premier rendez-vous. Pour les rendez-vous suivants, vous pourrez les prendre en ligne.&quot;,&quot;ErrorMessage_HomePageNewClientMinInfo&quot;: &quot;Vous devez entrer votre prénom, votre nom, votre adresse courriel et au moins un numéro de téléphone&quot;,&quot;ErrorMessage_BookingDependentMinInfo&quot;: &quot;Vous devez entrer le prénom et le nom de la personne à charge.&quot;,&quot;Title_ClientBookingNotAllowed&quot;: &quot;Prise de rendez-vous en ligne non disponible avec {0}&quot;,&quot;PasswordMeter_SimilarToUsername&quot;: &quot;Trop similaire au courriel&quot;,&quot;PasswordMeter_TooShort&quot;: &quot;Trop court&quot;,&quot;PasswordMeter_VeryWeak&quot;: &quot;Très faible&quot;,&quot;PasswordMeter_Weak&quot;: &quot;Faible&quot;,&quot;PasswordMeter_Good&quot;: &quot;Bon&quot;,&quot;PasswordMeter_Strong&quot;: &quot;Très bon!&quot;,&quot;ErrorMessage_BadPassword&quot;: &quot;Le mot de passe que vous avez entré n&#39;est pas valide.&quot;,&quot;Label_Vacation&quot;: &quot;Vacances&quot;,&quot;Yes&quot;: &quot;Oui&quot;,&quot;No&quot;: &quot;Non&quot;,&quot;Label_To&quot;: &quot;à&quot;,&quot;Message_OfflineWarning&quot;: &quot;Vous n&#39;êtes pas connecté à Internet alors vous pouvez voir seulement les rendez-vous de cette semaine.&quot;,&quot;LinkButton_Welcome&quot;: &quot;Bonjour {0}&quot;,&quot;Label_Signout&quot;: &quot;Me déconnecter&quot;,&quot;Label_WaitWhileLoading&quot;: &quot;Chargement en cours...&quot;,&quot;Message_DataRefreshElapseTime&quot;: &quot;Votre agenda a été rafraîchi il y a {0} minute(s).&quot;,&quot;ErrorMessage_ActivityTooFarInPast&quot;: &quot;Vous ne pouvez pas enregistrer ou annuler de rendez-vous, car la limite de temps permise est dépassée.&quot;,&quot;Label_AgeMonth&quot;: &quot;{0} mois&quot;,&quot;Label_AgeMonths&quot;: &quot;{0} mois&quot;,&quot;Label_AgeYear&quot;: &quot;{0} an&quot;,&quot;Label_AgeYears&quot;: &quot;{0} ans&quot;,&quot;Label_Available&quot;: &quot;Disponible&quot;,&quot;Label_Holiday&quot;: &quot;Jour férié&quot;,&quot;Label_Unavailable&quot;: &quot;Non disponible&quot;,&quot;Label_TimeAvailabilityIsNotVisibleToClients&quot;: &quot;Vos patients ne peuvent pas voir cette plage de disponibilité.&quot;,&quot;Label_CopyScheduleToWhichWeeks&quot;: &quot;L&#39;horaire ci-haut sera copié sur les semaines entre le {0} et le {1}.&quot;,&quot;Label_ActivityIsInPast&quot;: &quot;La date de ce rendez-vous est passée. Veuillez choisir une date postérieure à la date du jour.&quot;,&quot;Label_IsNewFeature&quot;: &quot;Nouveau!&quot;,&quot;Label_Select&quot;: &quot;Sélectionner&quot;,&quot;Label_SetTimeslotIntervalsTo&quot;: &quot;Afficher mon horaire à intervalles de&quot;,&quot;Label_XMinutes&quot;: &quot;{0} minutes&quot;,&quot;Label_DisplayMyScheduleFrom&quot;: &quot;Afficher mon horaire à partir de&quot;,&quot;Label_DisplayMyScheduleTo&quot;: &quot;Afficher mon horaire jusqu&#39;à&quot;,&quot;Label_ShowWeekendDays&quot;: &quot;Afficher les samedis et dimanches&quot;,&quot;Label_HideWeekendDays&quot;: &quot;Masquer les samedis et dimanches&quot;,&quot;Label_GoBack811&quot;: &quot;Nouvelle recherche&quot;,&quot;ColorPicker_BasicColors&quot;: &quot;Simples&quot;,&quot;ColorPicker_SavedColors&quot;: &quot;Récentes&quot;,&quot;ColorPicker_AdvancedColors&quot;: &quot;Avancées&quot;,&quot;ColorPicker_Hue&quot;: &quot;Teinte&quot;,&quot;ColorPicker_Ligthness&quot;: &quot;Luminosité&quot;,&quot;ColorPicker_Saturation&quot;: &quot;Saturation&quot;,&quot;ColorPicker_Preview&quot;: &quot;Aperçu&quot;,&quot;ColorPicker_BasicColorInstructions&quot;: &quot;Cliquez sur le le carré de gauche pour choisir la couleur&quot;,&quot;ColorPicker_AdvancedColorInstructions&quot;: &quot;Cliquez sur le le carré aperçu du bas pour choisir la couleur&quot;,&quot;Label_LoadMore&quot;: &quot;Voir la suite...&quot;,&quot;Label_ChooseAClient&quot;: &quot;Choisir le client&quot;,&quot;Placeholder_FindClient&quot;: &quot;Entrez le nom, tél ou courriel&quot;,&quot;Label_TypeClientName&quot;: &quot;Entrez le nom du client&quot;,&quot;Label_NotWithClient&quot;: &quot;Sans client&quot;,&quot;ComboOptionGroup_WaitingList&quot;: &quot;Liste d&#39;attente&quot;,&quot;ComboOptionGroup_AllClients&quot;: &quot;Tous les clients&quot;,&quot;ClientCompoItemLabel_TypeSomeLettersToSearch&quot;: &quot;Entrez quelques lettres pour chercher dans votre liste de clients&quot;,&quot;Column_AvailableTo&quot;: &quot;Disponible à&quot;,&quot;Label_AllClients&quot;: &quot;Tous les clients&quot;,&quot;Label_NewClients&quot;: &quot;Nouveaux clients&quot;,&quot;Label_ReturningClients&quot;: &quot;Clients existants&quot;,&quot;Label_NotAvailableOnline&quot;: &quot;Non disponible en ligne&quot;,&quot;professionalGroup_cancel_confirmation&quot;: &quot;Voulez-vous vraiment annuler les modifications que vous avez apportées?&quot;,&quot;Field_PhoneNumberIsMobileShort&quot;: &quot;Mobile:&quot;,&quot;Field_PhoneNumberIsHomeShort&quot;: &quot;Maison:&quot;,&quot;Field_PhoneNumberIsWorkShort&quot;: &quot;Travail:&quot;,&quot;Field_For&quot;: &quot;Pour :&quot;,&quot;Field_Receipt&quot;: &quot;Reçu :&quot;,&quot;Label_Age&quot;: &quot;Âge :&quot;,&quot;Label_GoodNews&quot;: &quot;Bonne nouvelle!&quot;,&quot;LinkButton_QuickSetDayAs&quot;: &quot;Vacances/férié&quot;,&quot;Column_PaymentStatus&quot;: &quot;Paiement&quot;,&quot;Label_NoPayment&quot;: &quot;Non payé&quot;,&quot;Label_PaidInCash&quot;: &quot;Payé comptant&quot;,&quot;Label_PaidByCheck&quot;: &quot;Payé par chèque&quot;,&quot;Label_PaidWithCreditCard&quot;: &quot;Payé par carte de crédit&quot;,&quot;Label_PaidWithInterac&quot;: &quot;Payé par Interac&quot;,&quot;Label_PaidWithGiftCard&quot;: &quot;Payé avec une carte cadeau&quot;,&quot;Label_PaidWithFidelityCard&quot;: &quot;Payé avec une carte fidelité&quot;,&quot;Label_ClickToContinueGMF&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,&quot;Label_ClickToContinueGEN&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,&quot;Label_ProfessionnalPageTitle&quot;: &quot;Label_ExternalBookWithProfessionnalTitle&quot;,&quot;ErrorMessage_SaveDataBeforeCancel&quot;: &quot;Veuillez enregistrer vos modifications avant d&#8217;annuler le rendez-vous.&quot;,&quot;Label_All&quot;: &quot;Tous&quot;,&quot;Label_Please_Select&quot;: &quot;Faire une sélection&quot;,&quot;nurse_811_timeout&quot;: &quot;Votre session est expirée. Vous serez redirigé vers la page de recherche&quot;,&quot;DeleteTimeAvailabilities_MenuItem&quot;: &quot;Supprimer des disponibilités&quot;,&quot;Label_Appointment&quot;: &quot;Rendez-vous&quot;,&quot;Label_NAM_short&quot;: &quot;NAM&quot;,&quot;ReportField_Date&quot;: &quot;Date et heure&amp;nbsp;:&quot;,&quot;Label_Type&quot;: &quot;Type&quot;,&quot;Info_FromDateTooltip&quot;: &quot;Cette plage deviendra publique à partir de la date et de l&#8217;heure indiquées, même si le professionnel de la santé a choisi l&#8217;option «&#8201;Limiter la prise de rendez-vous dans le futur&#8201;».&quot;,&quot;Field_PublicFrom&quot;: &quot;à partir de&#160;:&quot;,&quot;Field_IsTimeAvailabilityVisibleToClients&quot;: &quot;Publique&quot;,&quot;Label_Date&quot;: &quot;Date&quot;,&quot;Label_Time&quot;: &quot;Heure&quot;,&quot;Label_Duration&quot;: &quot;Durée&quot;,&quot;Label_Location&quot;: &quot;Lieu&quot;,&quot;Column_CellNumber&quot;: &quot;Tél. mobile&quot;,&quot;Label_Email&quot;: &quot;Courriel&quot;,&quot;Label_All_Archived_Included&quot;: &quot;Tous, incluant ceux qui sont supprimés&quot;,&quot;Message_AppointmentListPleaseWait&quot;: &quot;Recherche en cours. Veuillez patienter.&quot;,&quot;ErrorMessage_MaxScheduleRangeExceeded&quot;: &quot;Vous ne pouvez imprimer plus de {0} jours à la fois.&quot;,&quot;AppointmentFinder_ProfessionalXofY&quot;: &quot;Professionnel de la santé {0} de {1}&quot;,&quot;AppointmentFinder_DoctorXofY&quot;: &quot;Médecin {0} de {1}&quot;,&quot;AppointmentFinder_ResidentXofY&quot;: &quot;Résident {0} de {1}&quot;,&quot;AppointmentFinder_IPSXofY&quot;: &quot;IPS {0} de {1}&quot;,&quot;Label_Name&quot;: &quot;Nom&quot;,&quot;service_group_section_title&quot;: &quot;Regroupement de services&quot;,&quot;Title_MyServices&quot;: &quot;Services&quot;,&quot;DataTable_groupServiceEmptyFound&quot;: &quot;Aucun regroupement trouvé&quot;,&quot;ScheduleEditor_Search&quot;: &quot;Modifier ou supprimer un modèle&quot;,&quot;ScheduleEditor_Create&quot;: &quot;Créer un nouveau modèle&quot;,&quot;ScheduleEditor_Apply&quot;: &quot;Appliquer un modèle d&#39;horaire&quot;,&quot;Header_TitleModal&quot;: &quot;Professionnels qui pourront utiliser le modèle&quot;,&quot;Label_Patient&quot;: &quot;Patient&quot;,&quot;Label_MD&quot;: &quot;Professionnel de la santé&quot;,&quot;Column_HomeNumber&quot;: &quot;Tél. maison&quot;,&quot;Title_Activity&quot;: &quot;Activité&quot;,&quot;Title_Rendezvous&quot;: &quot;Rendez-vous&quot;,&quot;Label_UserIsBusy&quot;: &quot;{0} n&#39;est pas disponible&quot;,&quot;Label_UserIsAvailable&quot;: &quot;{0} est disponible&quot;,&quot;Label_UserIsOnVacation&quot;: &quot;{0} est en vacances&quot;,&quot;Label_UserIsOnHoliday&quot;: &quot;Jour férié&quot;,&quot;Message_RequestActivityTimeChange&quot;: &quot;{0} de {1} à {2}&quot;,&quot;Label_AppointmentConfirmed&quot;: &quot;Le client a reçu le rappel et a confirmé le rendez-vous.&quot;,&quot;Label_AppointmentReminderSent&quot;: &quot;Le rappel a été envoyé au client.&quot;,&quot;Field_To&quot;: &quot;À :&quot;,&quot;Field_From&quot;: &quot;De :&quot;,&quot;Label_MoneyAmount&quot;: &quot;{0}$&quot;,&quot;Label_SetupMyWorkSchedule&quot;: &quot;Label_SetupMyWorkSchedule&quot;,&quot;Label_XAppointmentsForPeriod&quot;: &quot;{0} rendez-vous pour la période&quot;,&quot;prof_not_allowed_in_location&quot;: &quot;Vous ne pouvez pas enregistrer la fiche de cet employé. Les infirmières praticiennes spécialisées (IPS) et les médecins résidents peuvent seulement être inscrits s&#8217;ils travaillent dans un établissement regroupé au sein d&#8217;un groupe de médecine de famille (GMF).&quot;,&quot;Message_OperationContextBookingWith&quot;: &quot;&lt;strong&gt;Prise de rendez-vous avec &lt;em&gt;{0}&lt;/em&gt;&lt;/strong&gt;.&lt;br/&gt;Pour prendre un rendez-vous, vous devez trouver une disponibilité indiquée par un bouton bleu affichant une heure et cliquer dessus.&quot;,&quot;Message_OperationContextBookingWithMobile&quot;: &quot;&lt;strong&gt;Prise de rendez-vous...&lt;/strong&gt;&quot;,&quot;ErrorMessage_MinimumServiceDuration&quot;: &quot;Veuillez indiquer une durée supérieure à 0.&quot;,&quot;ErrorMessage_InvalidMaxOverlappingAppointments&quot;: &quot;Le nombre maximum de clients simultanés pour un service doit être entre {0} et {1}.&quot;,&quot;error_message_missing_service_group_name&quot;: &quot;Inscrivez un nom pour ce regroupement de services.&quot;,&quot;error_message_choose_service&quot;: &quot;Spécifiez au moins un service.&quot;,&quot;error_message_all_reason_forbidden_in_servicegroup&quot;: &quot;Vous ne pouvez pas choisir cette raison&quot;,&quot;error_message_servicegroup_name_must_be_unique&quot;: &quot;Ce nom de regroupement de services existe déjà pour ce professionnel de la santé. Choisissez-en un autre.&quot;,&quot;Column_Name&quot;: &quot;Nom&quot;,&quot;Column_NameEmp&quot;: &quot;Type&quot;,&quot;Column_consultation_reason&quot;: &quot;Raison de consultation&quot;,&quot;Column_NameEnt&quot;: &quot;Nom&quot;,&quot;Column_Administrateurs&quot;: &quot;Administrateur(s)&quot;,&quot;Column_Phone&quot;: &quot;Téléphone&quot;,&quot;Column_StartDate&quot;: &quot;Date de début&quot;,&quot;Column_EndDate&quot;: &quot;Date de fin&quot;,&quot;Column_Rank&quot;: &quot;Priorité&quot;,&quot;Column_FirstName&quot;: &quot;Prénom&quot;,&quot;Column_LastName&quot;: &quot;Nom&quot;,&quot;Column_Age&quot;: &quot;Âge&quot;,&quot;Column_Selected&quot;: &quot;Sélectionné&quot;,&quot;Column_Profession&quot;: &quot;Profession&quot;,&quot;Column_Day&quot;: &quot;Jour&quot;,&quot;Column_FromTime&quot;: &quot;De&quot;,&quot;Column_ToTime&quot;: &quot;À&quot;,&quot;Column_Date&quot;: &quot;Date&quot;,&quot;Column_ReceiptNumber&quot;: &quot;Reçu&quot;,&quot;Column_Label&quot;: &quot;Libellé&quot;,&quot;Column_Location&quot;: &quot;Lieu&quot;,&quot;Column_IsIncludedInAllReasons&quot;: &quot;Inclus dans « toutes les raisons »&quot;,&quot;January&quot;: &quot;janvier&quot;,&quot;February&quot;: &quot;février&quot;,&quot;March&quot;: &quot;mars&quot;,&quot;April&quot;: &quot;avril&quot;,&quot;May&quot;: &quot;mai&quot;,&quot;June&quot;: &quot;juin&quot;,&quot;July&quot;: &quot;juillet&quot;,&quot;August&quot;: &quot;août&quot;,&quot;September&quot;: &quot;septembre&quot;,&quot;October&quot;: &quot;octobre&quot;,&quot;November&quot;: &quot;novembre&quot;,&quot;December&quot;: &quot;décembre&quot;,&quot;Label_AnyDay&quot;: &quot;N&#39;importe quel jour&quot;,&quot;Today&quot;: &quot;Aujourd&#39;hui&quot;,&quot;Tomorrow&quot;: &quot;Demain&quot;,&quot;Sunday&quot;: &quot;Dimanche&quot;,&quot;Monday&quot;: &quot;Lundi&quot;,&quot;Tuesday&quot;: &quot;Mardi&quot;,&quot;Wednesday&quot;: &quot;Mercredi&quot;,&quot;Thursday&quot;: &quot;Jeudi&quot;,&quot;Friday&quot;: &quot;Vendredi&quot;,&quot;Saturday&quot;: &quot;Samedi&quot;,&quot;Button_Add&quot;: &quot;Ajouter&quot;,&quot;Tooltip_CalendarNowBar&quot;: &quot;Cette barre bleue représente l&#39;heure actuelle.&quot;,&quot;Button_AddImage&quot;: &quot;Ajouter une image&quot;,&quot;Button_AddDocument&quot;: &quot;Ajouter un document&quot;,&quot;Message_SaveSuccess&quot;: &quot;L&#8217;employé a été ajouté avec succès.&quot;,&quot;Message_UpdateSuccess&quot;: &quot;L&#8217;employé a été mis à jour  avec succès.&quot;,&quot;Message_ConfirmDeleteItem&quot;: &quot;Voulez-vous vraiment supprimer cet élément? Vous ne pourrez pas le récupérer.&quot;,&quot;Message_ConfirmRecoverItem&quot;: &quot;Êtes-vous certains de vouloir récupérer cet item?&quot;,&quot;Label_Caution&quot;: &quot;Attention!&quot;,&quot;Label_Tags&quot;: &quot;Étiquettes&quot;,&quot;Label_PastActivities&quot;: &quot;Extension de délai&quot;,&quot;ErrorMessage_ActivityMovedInPast&quot;: &quot;Vous ne pouvez pas déplacer un rendez-vous futur vers le passé.&quot;,&quot;Label_Female&quot;: &quot;Femme&quot;,&quot;Label_Male&quot;: &quot;Homme&quot;,&quot;Label_InService&quot;: &quot;En service&quot;,&quot;Label_OutOfService&quot;: &quot;Hors service&quot;,&quot;Message_SessionAboutToExpire&quot;: &quot;Votre session est sur le point d&#39;expirer.&quot;,&quot;Message_SessionAboutToExpireTimeRemaining&quot;: &quot;Vous serez déconnecté dans {0} secondes.&quot;,&quot;Message_SessionAboutToExpireQuestion&quot;: &quot;Voulez-vous rester connecté?&quot;,&quot;Message_SessionAboutToExpireStaySignedIn&quot;: &quot;Oui&quot;,&quot;Message_SessionAboutToExpireSignOut&quot;: &quot;Non, je veux me déconnecter.&quot;,&quot;ErrorMessage_EmailAlreadyExists&quot;: &quot;Un compte avec ce courriel existe déjà.&quot;,&quot;ErrorMessage_SafariPrivateBrowsingNotSupported&quot;: &quot;Désolé, la &lt;strong&gt;navigation privée&lt;/strong&gt; avec Safari n&#39;est pas supportée.&lt;br/&gt;Veuillez naviguer en mode normal et essayez à nouveau.&quot;,&quot;Message_PasswordResetEmailSent&quot;: &quot;Si nous trouvons un compte associé à l&#39;adresse courriel que vous avez entré, vous recevrez un courriel pour choisir un nouveau mot de passe.&quot;,&quot;BookingWidget_PleaseEnterYourEmailAndTryAgain&quot;: &quot;Veuillez entrer votre adresse courriel dans le champs Courriel et essayez à nouveau&quot;,&quot;BookingWidget_OnlineBookingNotAvailable&quot;: &quot;La prise de rendez-vous en ligne n&#39;est pas encore disponible. Veuillez appeler au {0} pour prendre un rendez-vous.&quot;,&quot;BookingWidget_MyAppointments&quot;: &quot;Mes rendez-vous&quot;,&quot;BookingWidget_ExistingClientMenuTitle&quot;: &quot;Que voulez-vous faire?&quot;,&quot;BookingWidget_SessionExpired&quot;: &quot;Votre session est expirée. Vous devez entrer votre mot de passe à nouveau ou demander un lien sécurisé par courriel pour voir vos rendez-vous.&quot;,&quot;BookingWidget_ChangingAppointment&quot;: &quot;Modification du rendez-vous&quot;,&quot;BookingWidget_ChangeAppointmentConfirmation&quot;: &quot;Le rendez-vous à modifier sera annulé et un nouveau rendez-vous sera demandé.&quot;,&quot;Message_NoServiceAvailableForOnlineBookingPleaseEmail&quot;: &quot;Ce professionnel n&#39;a aucun service disponible pour la prise de rendez-vous en ligne pour le moment. Veuillez écrire à {0} pour demander un rendez-vous.&quot;,&quot;Message_NoServiceAvailableForOnlineBookingPleaseEmailOrCall&quot;: &quot;Ce professionnel n&#39;a aucun service disponible pour la prise de rendez-vous en ligne pour le moment. Veuillez écrire à {0} ou appeler au {1} pour demander un rendez-vous.&quot;,&quot;Message_ServiceUnavailableForMaintenance&quot;: &quot;Le service n&#39;est pas disponible en ce moment car nous effectuons une mise à jour. Veuillez revenir dans environ 30 minutes.&quot;,&quot;Message_NoAvailability&quot;: &quot;Aucune disponibilité pour la date demandée&quot;,&quot;Walktru_Next&quot;: &quot;Avancer&quot;,&quot;Walktru_Stop&quot;: &quot;Arrêter&quot;,&quot;Button_NextWalktru&quot;: &quot;Prochain « pas à pas »&quot;,&quot;Walktru_StepXofY&quot;: &quot;Étape {0} de {1}&quot;,&quot;Walktru_BusinessStart&quot;: &quot;Les informations de votre entreprises se trouvent ici.&quot;,&quot;Walktru_Save&quot;: &quot;Cliquez ici quand vous avez terminé.&quot;,&quot;Walktru_QTWeekChange&quot;: &quot;Utilisez ces flèches pour naviguer dans votre horaire. Vous pouvez cliquer directement sur la date pour ouvrir un calendrier et aller directement à une date choisie.&quot;,&quot;Walktru_QTViews&quot;: &quot;Vous pouvez changer le type d&#39;affichage ici. La vue mensuelle est sourtout utile pour ajouter des jours fériés.&quot;,&quot;Walktru_QTChooseProfessional&quot;: &quot;Walktru_QTChooseProfessional&quot;,&quot;Walktru_QTToolbarEdit&quot;: &quot;Modifiez les informations, services, reçus, horaire de travail, etc.&quot;,&quot;Walktru_QTToolbarPrint&quot;: &quot;Imprimez votre horaire ou rapport de revenus de la journée, de la semaine ou du mois.&quot;,&quot;Walktru_QTPreferences&quot;: &quot;Modifiez vos informations personnelles et vos préférences d&#39;affichage de l&#39;agenda.&quot;,&quot;Walktru_QTBusiness&quot;: &quot;Modifiez les informations, options, adresses, employés, etc. de votre entreprise.&quot;,&quot;Walktru_QTClients&quot;: &quot;Gérez votre liste de clients&quot;,&quot;Walktru_QTWaitingList&quot;: &quot;Gérer votre liste d&#39;attente. Pour ajouter un client à votre liste d&#39;attente, allez dans votre liste de clients et glissez-déposez un client sur ce bouton.&quot;,&quot;Walktru_QTEnd&quot;: &quot;La visite rapide est terminée! Veuillez regarder les autres points pour aller plus en profondeur.&quot;,&quot;Walktru_CMList&quot;: &quot;Voici votre liste de clients.&quot;,&quot;Walktru_CMAddingClients&quot;: &quot;Cliquez ici pour ajouter un nouveau client&quot;,&quot;Walktru_CMClientInfo&quot;: &quot;Vous devez remplir les champs obligatoires indiqués par une étoile.&quot;,&quot;Walktru_CMClientEmail&quot;: &quot;Si vous ajouter l&#39;adresse courriel du client, nous l&#39;inviterons à utiliser RDVS pour prendre leur prochain rendez-vous en ligne, vous permettant d&#39;économiser temps et argent!&lt;br/&gt;&lt;br/&gt;Nous allons aussi envoyer un rappel par courriel deux jours avant un rendez-vous à partir duquel le client pourra confirmer ou annuler sa présence. Vous allez voir un crochet sur le rendez-vous quand le client aura confirmé.&quot;,&quot;Walktru_CMClientCellNumber&quot;: &quot;Si vous ajoutez le numéro de mobile (portable) d&#39;un client, nous allons lui envoyer un rappel par message texte deux jours avant son rendez-vous à partir duquel le client pourra confirmer ou annuler sa présence.&quot;,&quot;Walktru_CMClientAdd&quot;: &quot;Ensuite, cliquez ici pour sauvegarder le nouveau client&quot;,&quot;Walktru_CMClientSchedule&quot;: &quot;Finalement, pour créer un rendez-vous, vous n&#39;avez qu&#39;à glisser-déposer le client à l&#39;heure désirée dans votre horaire.&quot;,&quot;Walktru_CMEnd&quot;: &quot;C&#39;est tout!&lt;br/&gt;Quoi? Pensiez-vous que c&#39;était plus compliqué que ça?&quot;,&quot;ErrorMessage_TimeAvailabilityTemplate_Limit&quot;: &quot;Le nombre de semaines pour appliquer un modèle doit se situer entre 1 et 52.&quot;,&quot;Walktru_WSStart&quot;: &quot;La première chose à faire est de créer votre horaire de travail qui sera utilisé pour offrir les heures de rendez-vous disponibles à vos clients.&quot;,&quot;Walktru_WSUpdateDefault&quot;: &quot;Walktru_WSUpdateDefault&quot;,&quot;Walktru_WSUpdateCurrent&quot;: &quot;Walktru_WSUpdateCurrent&quot;,&quot;Walktru_WSCopy&quot;: &quot;Si vous voulez créer un horaire d&#39;été, vous n&#39;avez qu&#39;a définir l&#39;horaire pour la première semaine et la copier sur un nombre de semaines à venir.&quot;,&quot;Walktru_WSDone&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;Cliquez dans l&#39;horaire pour créer vos périodes de disponibilité.&lt;/p&gt;&lt;p class=&#39;Interligne&#39;&gt;Les heures de rendez-vous disponibles seront affichées à vos clients selon vos périodes définies et la durée de vos services.&lt;br/&gt;Par exemple, si votre service dure une heure et que vous avez créé une période de 9h à 12h, les heures de rendez-vous disponibles pour vos clients seront 9h, 10h et 11h.&lt;/p&gt;&lt;p&gt;Cliquez ici quand vous avez terminé. Vous pouvez modifier votre horaire à tout moment.&lt;/p&gt;&quot;,&quot;Walktru_WSEnd&quot;: &quot;Voila! Votre horaire de travail est créé&quot;,&quot;Walktru_HPHomePageTab&quot;: &quot;&lt;p class=&#39;Interligne&#39;&gt;Cliquez ici pour configurer votre page d&#39;accueil.&lt;/p&gt;&lt;p class=&#39;Interligne&#39;&gt;Vous pouvez publier le lien de votre page sur Facebook, LinkedIn ou tout autre endroit pour permettre à vos clients de vous trouver et de prendre un rendez-vous.&lt;/p&gt;&lt;p&gt;Cette page vous donne une visibilité en ligne. Vous pouvez aussi facilement ajouter un bouton « Prendre un rendez-vous » sur votre propre site web si vous en avez déjà un.&lt;/p&gt;&quot;,&quot;Walktru_HPPageName&quot;: &quot;Premièrement, vous devez choisir un nom unique pour votre page qui formera le lien web. Par exemple, vous pouvez choisir votre nom ou le nom de votre entreprise ou clinique.&quot;,&quot;Walktru_HPPageNameAvailability&quot;: &quot;Cliquez ici pour vérifier que le nom est disponible&quot;,&quot;Walktru_HPLogo&quot;: &quot;Téléchargez votre logo ou votre photo&quot;,&quot;Walktru_HPMessage&quot;: &quot;Écrivez un mot d&#39;accueil et une description de vos services ici.&quot;,&quot;Walktru_HPWidgetButton&quot;: &quot;Pour ajouter un bouton « Prendre un rendez-vous » sur votre propre site web, ajoutez ces lignes HTML à votre site.&quot;,&quot;Walktru_HPSave&quot;: &quot;Quand vous avez terminé, cliquez sur ici pour enregistrer vos changements.&quot;,&quot;Walktru_HPPreview&quot;: &quot;Cliquez sur ce lien pour voir votre page.&quot;,&quot;Walktru_HPEnd&quot;: &quot;Terminé! Votre page d&#39;accueil est prête à recevoir vos clients.&quot;,&quot;Walktru_EmployeeTab&quot;: &quot;C&#39;est ici que vous pouvez gérer votre équipe de professionnels incluant vous-même.&quot;,&quot;Walktru_EmployeeList&quot;: &quot;Voici votre équipe de professionnels. Cliquez sur un professionnel pour mettre à jour ses informations, créer son horaire de travail, ses reçus personnalisés et ses services offerts.&quot;,&quot;Walktru_CSServices&quot;: &quot;Ici se trouve la liste des services offerts par ce professionnel.&quot;,&quot;Walktru_CSAdd&quot;: &quot;Cliquez ici pour ajouter un nouveau service.&quot;,&quot;Walktru_CSName&quot;: &quot;Entrez le nom du service ici. Ce nom sera affiché sur les reçus d&#39;assurance.&quot;,&quot;Walktru_CSReceipt&quot;: &quot;Vous pouvez associer un modèle de reçu à votre service en le sélectionnant de la liste. Vous pouvez en créer un nouveau en cliquant sur ce bouton.&quot;,&quot;Walktru_CSReceiptName&quot;: &quot;Entrer le nom du modèle de reçu. Ce nom sert seulement à identifier le modèle de recû pour l&#39;associer à vos services.&quot;,&quot;Walktru_CSReceiptTitle&quot;: &quot;Entrez votre titre professionnel que vous voulez afficher sur le reçu.&quot;,&quot;Walktru_CSReceiptInfo&quot;: &quot;Entrer les autres informations à afficher sur vos reçus tels que votre numéro de membre de votre association.&quot;,&quot;Walktru_CSEnd&quot;: &quot;Excellent! Vous avez créé un service associé à un modèle de reçu d&#39;assurance.&quot;,&quot;Walktru_CNBusinessClientNotificationTab&quot;: &quot;Ici, vous pouvez écrire un message personnalisé qui sera inclus dans tous les courriels de notifications envoyés à vos clients.&quot;,&quot;Walktru_CNBusinessClientNotificationContent&quot;: &quot;Écrivez le message à inclure dans les courriels. Par exemple, vous pourriez écrire votre politique d&#39;annulation.&quot;,&quot;Walktru_CNEmployeeTab&quot;: &quot;Vous pouvez aussi définir un message personnalisé par professionnel si vous avez une équipe de professionnels dans votre entreprise.&quot;,&quot;Walktru_CNEmployeeList&quot;: &quot;Cliquez sur le professionnel&quot;,&quot;Walktru_EmployeeClientNotificationTab&quot;: &quot;Cliquez sur cette section pour écrire le message.&quot;,&quot;Walktru_EmployeeClientNotification&quot;: &quot;Écrivez le message à inclure dans les courriels. Ce message sera ajouté aux courriels après celui défini pour l&#39;entreprise.&quot;,&quot;Walktru_CNEnd&quot;: &quot;Terminé. Vos courriels de notifications clients vont être génials!&quot;,&quot;Walktru_ASButton&quot;: &quot;Cliquez sur Paramètres du compte pour configurer vos options d&#39;entreprise.&quot;,&quot;Walktru_ASBusinessInfo&quot;: &quot;Vous voyez qu&#39;il y a quelques onglets dans le haut, à droite de celui-ci. Cet onglet permet de définir vos lieux, salles, équipements, etc.&quot;,&quot;Walktru_ASBusinessInfo_SubTabs&quot;: &quot;Dans chacun des onglets, vous trouverez des sous-onglets contenant des options par sujet.&quot;,&quot;Walktru_ASEmployeesNServices&quot;: &quot;Cet onglet permet de gérer vos employés. Pour chaque employé, vous pouvez définir leurs services, leurs reçus, leurs options de prise de rendez-vous, leurs permissions d&#39;accès, etc.&quot;,&quot;Walktru_ASEmployeesNServices_EmployeeSelector&quot;: &quot;Cliquez sur ce menu déroulant pour sélectionner un autre employé. Pour ajouter des employés, cliquez sur le bouton plus bas. Seul l&#39;administrateur du compte peut ajouter des employés. Les employés pourront gérer eux-mêmes leurs préférences.&quot;,&quot;Walktru_ASClientCommunications&quot;: &quot;Cet onglet vous permet de configurer vos communications clients tel que les rappels de rendez-vous, la personnalisation des courriels, etc.&quot;,&quot;Walktru_ASPublishNPromote&quot;: &quot;Ici, nous vous aidons à publier et promouvoir vos services en ligne.&quot;,&quot;Walktru_ASTestClientBooking&quot;: &quot;Utilisez cet onglet pour valider la prise de rendez-vous qu&#39;expérimenteront vos clients. Toutes vos configurations de prise de rendez-vous peuvent être validées ici.&quot;,&quot;Walktru_ASEnd&quot;: &quot;C&#39;est tout!&quot;,&quot;Walktru_ASNewSetupUI&quot;: &quot;Nous venons d&#39;investir dans de l&#39;espace d&#39;écran et avons déménagé les paramètres du compte du petit panneau de gauche vers un superbe espace ouvert! Nous pouvons maintenant vous aider davantage à personnaliser vos services ainsi que les courriels envoyés aux clients et vérifier la prise prise de rendez-vous.&quot;,&quot;Label_AM&quot;: &quot;AM&quot;,&quot;Label_PM&quot;: &quot;PM&quot;,&quot;Label_Evening&quot;: &quot;Soir&quot;,&quot;Template_NoProfessions&quot;: &quot;Veuillez indiquer au moins une profession.&quot;,&quot;Label_EstablishmentsAvailabilityGMF&quot;: &quot;Cliniques de votre GMF offrant des disponibilités :&quot;,&quot;Label_EstablishmentsAvailability&quot;: &quot;Les cliniques suivantes offrent des disponibilités pour votre rendez-vous&#160;:&quot;,&quot;Label_Oops&quot;: &quot;Attention!&quot;,&quot;Label_BookingCompleted&quot;: &quot;La prise de votre rendez-vous est complétée.&quot;,&quot;Label_BookingCompletedWithEmailText&quot;: &quot;La prise de votre rendez-vous est complétée. Vous recevrez une confirmation sous peu, selon le mode de communication choisi.&quot;,&quot;Label_ConfirmationEmail&quot;: &quot;courriel&quot;,&quot;Label_ConfirmationTextMessage&quot;: &quot;SMS&quot;,&quot;Label_ConfirmationPhone&quot;: &quot;Téléphone&quot;,&quot;Label_By&quot;: &quot;Par&quot;,&quot;Label_And&quot;: &quot;et&quot;,&quot;Label_LanguageFrench&quot;: &quot;Français&quot;,&quot;Label_LanguageEnglish&quot;: &quot;Anglais&quot;,&quot;Label_ServiceType&quot;: &quot;Service(s)&quot;,&quot;Label_EnterContactInformation&quot;: &quot;Veuillez saisir vos coordonnées&quot;,&quot;Label_CellPhone&quot;: &quot;Téléphone mobile&quot;,&quot;Label_HomePhone&quot;: &quot;Téléphone à la maison&quot;,&quot;Label_DateOfBirth&quot;: &quot;Date de naissance&quot;,&quot;Label_EmailAssure&quot;: &quot;Courriel&quot;,&quot;Label_MediumText&quot;: &quot;SMS&quot;,&quot;Label_MediumPhone&quot;: &quot;Téléphone&quot;,&quot;Label_LanguageCommunication&quot;: &quot;Langue de communication&quot;,&quot;Label_ConfirmationReminders&quot;: &quot;Confirmations et rappels&quot;,&quot;Label_SummaryInformation&quot;: &quot;Coordonnées&quot;,&quot;Button_ConfirmAppointment&quot;: &quot;Confirmer le rendez-vous&quot;,&quot;Label_AppointmentListShort&quot;: &quot;Liste des rendez-vous&quot;,&quot;AiPP_Period_Range_Title&quot;: &quot;Période de dérogation&quot;,&quot;quickSearch_NAM_not_found&quot;: &quot;NAM introuvable&quot;,&quot;Label_UpdateOrCancelAppointment&quot;: &quot;Mettre à jour mes coordonnées ou annuler mon rendez-vous du&quot;,&quot;Label_UpdateAppointment&quot;: &quot;Mettre à jour mes coordonnées pour mon rendez-vous &#39;&#39;{0}&#39;&#39;, du&quot;,&quot;Label_CancelAppointment&quot;: &quot;Annuler mon rendez-vous du&quot;,&quot;sZeroRecordsModal&quot;: &quot;Aucun modèle n&#8217;est disponible.&quot;,&quot;sZeroRecordsLocation&quot;: &quot;Aucun lieu n&#8217;est disponible.&quot;,&quot;Message_ConfirmationWithNotification&quot;: &quot;&lt;p class=&#39;Interligne&#39; &gt;Vos coordonnées ont été mises à jour avec succès.&quot;,&quot;Message_ConfirmationWithoutNotification&quot;: &quot;&lt;p class=&#39;Interligne&#39; &gt;Vos coordonnées ont été mises à jour.&lt;/p&gt; &lt;p&gt;Vous recevrez une confirmation sous peu, selon le mode de communication choisi.&quot;,&quot;Message_CancellationOK&quot;: &quot;Votre rendez-vous est annulé.&quot;,&quot;InformationMessage_ContactInfo&quot;: &quot;Vous devez mettre à jour vos coordonnées pour &lt;b&gt;chacun de vos rendez-vous.&lt;/b&gt; Pour ce faire, vous devrez utiliser le numéro de référence qui correspond au rendez-vous concerné.&quot;,&quot;WarningMessage_RemainingTime&quot;: &quot;Temps restant pour confirmer le rendez-vous&quot;,&quot;Activity_Booking_Title&quot;: &quot;Temps restant pour lequel le rendez-vous vous est réservé en priorité :&quot;,&quot;Activity_Booking_Extend&quot;: &quot;Attention! Le délai de 3 minutes durant lequel le rendez-vous vous est réservé en priorité sera bientôt terminé. Désirez-vous prolonger ce délai?&quot;,&quot;Activity_SomeTA_NotShown&quot;: &quot;Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de votre professionnel de la santé ou cliquer sur le bouton ci-dessous.&quot;,&quot;Activity_SomeTA_NotShownGmf&quot;: &quot;Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de ses professionnels de la santé ou cliquer sur le bouton ci-dessous.&quot;,&quot;InformationMessage_AnnulationFeesGen&quot;: &quot;Lors de ce rendez-vous, la clinique pourrait vous confier aux soins d&#8217;un autre professionnel de la santé. De plus, elle se réserve le droit de vous imposer des frais en cas de retard, d&#8217;absence ou d&#8217;annulation.&quot;,&quot;Limit_AnnulationFees&quot;: &quot;Vous ne pouvez annuler le rendez-vous en ligne à moins de {0} d&#8217;avis. Au-delà de cette période, vous devez téléphoner directement à la clinique au numéro affiché ci-dessus.&quot;,&quot;InformationMessage_AnnulationFeesMf&quot;: &quot;La clinique se réserve le droit de vous imposer des frais en cas de retard, d&#39;absence ou d&#39;annulation.&quot;,&quot;InformationMessage_AnnulationFees&quot;: &quot;Cette clinique ne permet pas d&#39;annuler un rendez-vous prévu dans un délai de moins de {0} {1}. Dans un tel cas, vous devez communiquer avec cette dernière par téléphone au numéro affiché ci-dessus. Notez que la clinique peut vous imposer des frais en cas de retard, d&#39;absence ou d&#39;annulation.&quot;,&quot;Message_WarningReservation&quot;: &quot;Ce rendez-vous est réservé.&quot;,&quot;Message_AppointmentWithNotification&quot;: &quot;&lt;span class=&#39;ConfirmationAssure confirmDate&#39;&gt;Votre rendez-vous du &lt;strong&gt;&lt;/strong&gt; a été enregistré avec succès. Vous recevrez une confirmation sous peu. &lt;/span&gt;&lt;br/&gt;&lt;span class=&#39;ConfirmationAssure&#39;&gt;Numéro de référence&#160;: &lt;strong&gt;{0}&lt;/strong&gt;&lt;/span&gt;&lt;br/&gt;&lt;span class=&#39;ConfirmationAssure&#39;&gt;Veuillez conserver ce numéro si vous avez sélectionné uniquement le téléphone comme mode de communication, car il est requis pour annuler votre rendez-vous ou mettre à jour vos coordonnées. &lt;/span&gt;&quot;,&quot;Message_AppointmentWithoutNotification&quot;: &quot;&lt;span class=&#39;ConfirmationAssure confirmDate&#39;&gt;Votre rendez-vous du &lt;strong&gt;&lt;/strong&gt; a été enregistré avec succès. Vous recevrez une confirmation sous peu, selon le mode de communication choisi.&lt;/span&gt;&lt;br/&gt;&lt;span class=&#39;ConfirmationAssure&#39;&gt;Numéro de référence&#160;: &lt;strong&gt;{0}&lt;/strong&gt;&lt;/span&gt;&lt;br/&gt;&lt;span class=&#39;ConfirmationAssure&#39;&gt;Veuillez conserver ce numéro, il est requis pour annuler votre rendez-vous ou mettre à jour vos coordonnées sans recourir au lien du courriel ou du SMS.&lt;/span&gt;&quot;,&quot;Avertissement_AppointmentWithNotification_withoutClinic&quot;: &quot;&lt;span class=&#39;ConfirmationAssure&#39;&gt;Il est possible que vous ne recevriez pas de confirmation par courriel ou par message texte. &lt;/strong&gt; Nous vous recommendons donc de noter la date et l&#39;heure de votre rendez-vous. &lt;/strong&gt; &quot;,&quot;InformationMessage_CardRequired&quot;: &quot;Au moment du rendez-vous, vous devez présenter votre carte d&#8217;assurance maladie valide. Si vous oubliez de la présenter ou si elle est expirée, vous devrez payer pour recevoir des &lt;a href=&#39;https://www.ramq.gouv.qc.ca/fr/citoyens/assurance-maladie/services-couverts/Pages/services-medicaux.aspx&#39; target=&#39;_blank&#39;&gt;services couverts&lt;/a&gt;.&quot;,&quot;ErrorMessage_ExceededCancellationDeadline&quot;: &quot;Vous ne pouvez pas annuler un rendez-vous en ligne à moins de {0} d&#8217;avis. Téléphonez directement à la clinique au numéro qui apparaît ci-dessus pour annuler votre rendez-vous.&quot;,&quot;ErrorMessage_WriteActionNotAllowed&quot;: &quot;Selon vos permissions, vous pouvez uniquement consulter cet agenda&quot;,&quot;Label_FindAppointment&quot;: &quot;Prendre rendez-vous&quot;,&quot;professionalGroup_error_empty_group&quot;: &quot;Le groupe de travail est vide. Vous ne pouvez pas l&#8217;enregistrer.&quot;,&quot;ErrorMessage_AppointmentNotAvailable&quot;: &quot;Cette plage n&#8217;est plus disponible. Veuillez svp choisir un autre rendez-vous.&quot;,&quot;ErrorMessage_EmailAddressInvalid&quot;: &quot;Veuillez entrer une adresse courriel valide. Vérifiez qu&#39;il n&#39;y a pas d&#39;erreur de saisie.&quot;,&quot;Label_BookAppointmentFor&quot;: &quot;Prendre rendez-vous pour « {0} » le&quot;,&quot;Message_NoBookingAvailable&quot;: &quot;Cette clinique n&#39;offre pas de rendez-vous en ligne après cette date.&quot;,&quot;Label_ForgotPassword&quot;: &quot;Mot de passe oublié&quot;,&quot;Label_Doctor&quot;: &quot;Professionnel de la santé&quot;,&quot;ErrorMessage_InvalidNAM&quot;: &quot;Le numéro d&#39;assurance maladie est invalide.&quot;,&quot;Label_LinkPortalSanteMieuxEtre&quot;: &quot;Portail santé mieux-être&quot;,&quot;Label_LinkPortalSanteMieuxEtre_URL&quot;: &quot;http://sante.gouv.qc.ca/repertoire-ressources/consultations-medicales-sans-rendez-vous/?triPar=service&amp;ch_service=1&amp;ch_nom=&amp;choixTerritoire=cp&amp;ch_code=A1A1A1&amp;utm_source=RechercheParCP-RAMQ&amp;utm_campaign=RSVQ-RAMQ&quot;,&quot;Label_PortalSanteMieuxEtre&quot;: &quot;Trouver d&#8217;autres ressources sur le&quot;,&quot;Button_Restart&quot;: &quot;Recommencer&quot;,&quot;ErrorMessage_InvalidLocation&quot;: &quot;[medecin] n&#8217;est disponible que dans le lieu [lieu] au moment choisi.&quot;,&quot;ErrorMessage_ActivityIsInPast&quot;: &quot;Vous ne pouvez pas enregistrer ou annuler ce rendez-vous, car sa date est passée depuis plus de 90 jours.&quot;,&quot;ErrorMessage_Pilot&quot;: &quot;&lt;p&gt;Nous sommes désolés, mais vous n&#8217;avez pas accès à Rendez-vous santé Québec pour le moment.&lt;/p&gt;&lt;p&gt;Le nouveau service fait présentement l&#8217;objet d&#8217;un déploiement progressif. Seules les personnes qui résident dans la ou les régions visées ou qui ont un professionnel de la santé dans l&#8217;une des &lt;a href=&#39;https://rvsq.gouv.qc.ca/accueil/index.html&#39; target=&#39;_blank&#39;&gt;cliniques participantes&lt;/a&gt; peuvent prendre rendez-vous en ligne.&lt;/p&gt;&lt;p&gt;Vous pourrez utiliser le service dans les prochains mois.&lt;/p&gt;&quot;,&quot;Message_NoAddressFound&quot;: &quot;Aucune adresse trouvée&quot;,&quot;Label_FurtherOptionsAvailable&quot;: &quot;Si les disponibilités ne vous conviennent pas, vous pourrez prendre rendez-vous avec un autre professionnel de la santé aux étapes suivantes.&quot;,&quot;Tooltip_PostalCode&quot;: &quot;Inscrivez votre code postal ou tout autre code postal près duquel vous souhaitez trouver une clinique.&quot;,&quot;Label_ConfirmAppointment&quot;: &quot;Confirmation du rendez-vous {1} pour « {0} » :&quot;,&quot;Label_ConfirmAppointment&quot;: &quot;Confirmation du rendez-vous {1} pour « {0} » :&quot;,&quot;Warning_AvailabilityWithPublicFrom&quot;: &quot;L&#39;horaire que vous êtes sur le point de reproduire contient des plages de disponibilité qui seront publiées ultérieurement.&quot;,&quot;ErrorMessage_InvalidPublicationDateTime&quot;: &quot;La date et l&#8217;heure d&#8217;ouverture de la plage doivent être comprises entre la date et l&#8217;heure actuelle et la date et l&#8217;heure de fin de la plage.&quot;,&quot;ErrorMessage_SlotDurationShorterThanService&quot;: &quot;La plage horaire sélectionnée est inférieure à la durée minimale du service. Veuillez modifier la plage horaire ou choisir un service de plus courte durée.&quot;,&quot;ErrorMessage_ActivityMandatoryLastName&quot;: &quot;Veuillez indiquer le nom.&quot;,&quot;ErrorMessage_DloMustHaveName&quot;: &quot;Veuillez indiquer le nom.&quot;,&quot;ErrorMessage_DloMustHaveStartDate&quot;: &quot;Veuillez indiquer la date de début.&quot;,&quot;Label_a&quot;: &quot;à&quot;,&quot;Label_Places_Informations&quot;: &quot;Informations&quot;,&quot;Label_ConfirmBoxMoveLocationToCompanyOrigin&quot;: &quot;Au moins un lieu de l&#8217;entreprise dispose d&#8217;un message par défaut. En continuant, vous remplacerez le message par défaut par celui que vous venez de définir pour l&#8217;entreprise.&quot;,&quot;Title_Copy&quot;: &quot;Copier&quot;,&quot;Label_Range&quot;: &quot;Période&quot;,&quot;Label_Activated&quot;: &quot;Activé&quot;,&quot;Label_ActiveAppointment&quot;: &quot;Actif&quot;,&quot;Select2_NoMatches&quot;: &quot;Aucun item trouvé&quot;,&quot;ErrorMessage_Exemption_AlreadyExists&quot;: &quot;Une dérogation est déjà définie pour un ou plusieurs professionnels de la santé durant cette période pour le ou les lieux sélectionnés.&quot;,&quot;ErrorMessage_StartDateInPast&quot;: &quot;La date de début doit être postérieure ou égale à la date du jour.&quot;,&quot;ErrorMessage_EndDateInPast&quot;: &quot;La date de fin doit être postérieure ou égale à la date de début.&quot;,&quot;Label_exemption&quot;: &quot;Accorder une période de dérogation&quot;,&quot;Label_exemption_range&quot;: &quot;Période de dérogation&quot;,&quot;Error_EmptyCancelLimit&quot;: &quot;Le délai maximum pour l&#8217;annulation en ligne doit se situer entre 0 et 59 minutes ou entre 0 et 24 heures.&quot;,&quot;Label_RAMQ_Name&quot;: &quot;Régie de l&#39;assurance maladie&quot;,&quot;Label_LocationMessagesTypeMFDescription&quot;: &quot;Ce message apparaît au bas de l&#8217;agenda lorsqu&#8217;une personne recherche les disponibilités de son professionnel de la santé.&quot;,&quot;Label_LocationMessagesTypeMFDescriptionNOGMF&quot;: &quot;Ce message apparaît au bas de l&#8217;agenda lorsqu&#8217;une personne non inscrite en groupe de médecine de famille (GMF) recherche les disponibilités de son professionnel de la santé.&quot;,&quot;Label_LocationMessagesTypeMFDescriptionGMF&quot;: &quot;Ce message apparaît au bas de l&#8217;agenda lorsqu&#8217;une personne inscrite en groupe de médecine de famille (GMF) recherche les disponibilités de son professionnel de la santé.&quot;,&quot;Label_LocationMessagesTypeGMFDescription&quot;: &quot;Ce message apparaît au bas de l&#8217;agenda lorsqu&#8217;une personne recherche les disponibilités d&#8217;un autre professionnel de la santé de son Groupe de médecine de famille (GMF).&quot;,&quot;Label_LocationMessagesTypeMF&quot;: &quot;Message sur les disponibilités du professionnel de la santé d&#8217;un patient&quot;,&quot;Error_SpecialChars&quot;: &quot;Le champ de la limite d&#39;annulation en ligne ne peut pas contenir des caractères spéciaux.&quot;,&quot;Label_LocationMessagesTypeMF_GMF&quot;: &quot;Message sur les disponibilités du professionnel de la santé d&#39;un patient en groupe de médecine de famille (GMF) &quot;,&quot;Label_LocationMessagesTypeGMF&quot;: &quot;Message sur les disponibilités&#160;d&#8217;un autre professionnel de la santé du groupe de médecine de famille (GMF)&quot;,&quot;Label_LocationMessagesTypeMF_NO_GMF&quot;: &quot;Message sur les disponibilités du professionnel de la santé d&#39;un patient hors groupe de médecine de famille (GMF) &quot;,&quot;Label_LocationMessagesTypeMF_NO_GMFDescription&quot;: &quot;Ce Message sur les disponibilités d&#39;un autre professionnel de la santé du groupe de médecine de famille (GMF) pas de GMF.&quot;,&quot;Label_LocationMessages_above_button_GMF&quot;: &quot;Label_LocationMessages_above_button_GMF&quot;,&quot;Label_LocationMessages_above_button_MF&quot;: &quot;Veuillez cliquer sur le bouton ci-dessous pour voir les disponibilités des autres professionnels de la santé de votre groupe de médecine de famille (GMF).&quot;,&quot;Label_LocationMessages_above_button_NO-GMF&quot;: &quot;Label_LocationMessages_above_button_NO_GMF&quot;,&quot;Label_Places_Options_DisplayModeTitle&quot;: &quot;Mode d&#39;affichage des disponibilités&quot;,&quot;Label_Places_Options_ClientType&quot;: &quot;Par type de clientèle&quot;,&quot;Label_Places_Options_PatientProfessionalRelation&quot;: &quot;Par relation patient / professionnel de la santé&quot;,&quot;Label_DisplayModeByTypeDescr&quot;: &quot;Les disponibilités publiques sont affichées en fonction du type défini dans les plages de disponibilités. Ex. : une personne assurée prenant rendez-vous avec son professionnel de la santé ne verra que les plages  « Medecin de famille (MF) » offertes par ce dernier. Elle devra prendre rendez-vous en groupe de médecine de famille (GMF) ou par proximité pour voir ses autres plages.&quot;,&quot;Label_DisplayModeByGRLDescr&quot;: &quot;Les disponibilités publiques sont affichées en fonction de la relation entre le patient et son professionnel de la santé. Ex. : une personne assurée prenant rendez-vous avec son professionnel de la santé verra toutes les plages offertes par ce dernier et ne les verra plus si elle poursuit sa recherche en groupe de médecine de famille (GMF) ou par proximité.&quot;,&quot;Label_Anonymize_Option_Gmf&quot;: &quot;Pour la prise de rendez-vous en groupe de médecine de famille (GMF)&quot;,&quot;Label_Places_Option_Time&quot;: &quot;Politique d&#8217;annulation&quot;,&quot;Label_Places_Option_Time_Desc&quot;: &quot;Délai maximum à partir duquel le patient ne peut plus annuler son rendez-vous en ligne et où il est invité à téléphoner à la clinique pour l&#8217;annuler.&quot;,&quot;Label_Anonymize_Option_Gen&quot;: &quot;Pour la prise de rendez-vous à proximité&quot;,&quot;Label_Anonymize_Option_Title&quot;: &quot;Masquer le nom des professionnels de la santé&quot;,&quot;Label_AllowNoNam_Title&quot;: &quot;Prise de rendez-vous sans numéro d&#39;assurance maladie&quot;,&quot;Label_AllowNoNam_Desc&quot;: &quot;Permettre la prise de rendez-vous par une infirmière en réorientation (811, centre hospitalier ou autre clinique) pour une personne qui n&#8217;a pas de carte d&#8217;assurance maladie&quot;,&quot;Label_AllowNoNam_Hint&quot;: &quot;À noter : En période d&#8217;urgence sanitaire, vous pouvez facturer les services offerts à un patient qui n&#8217;a pas de carte d&#8217;assurance maladie valide à la RAMQ&quot;,&quot;Label_isSending3DayReminders_Option_Title&quot;: &quot;Gestion des rappels&quot;,&quot;Label_isSending3DayReminders_Switch&quot;: &quot;Envoyer un rappel au patient 3 jours avant la date de son rendez-vous&quot;,&quot;Label_isSending3DayReminders_Descr&quot;: &quot;Dans tous les cas, notez qu&#8217;un rappel est envoyé automatiquement la veille du rendez-vous, entre 8 h et 21 h.&quot;,&quot;Label_isProfNameHiddenDescr&quot;: &quot;&lt;strong&gt;Oui:&lt;/strong&gt; Les patients ne voient pas le nom des professionnels de la santé. Rendez-vous santé Québec en choisit un pour eux quand plusieurs professionnels de la santé offrent la même disponibilité.&lt;br /&gt;&lt;br /&gt;&lt;strong&gt;Non:&lt;/strong&gt; Les patients voient le nom des professionnels de la santé et peuvent choisir celui qu&#8217;ils veulent.&quot;,&quot;Label_Tooltip_Anonymized&quot;: &quot;Identité du professionnel de la santé invisible pour le patient&quot;,&quot;Label_Tooltip_Not_Anonymized&quot;: &quot;Identité du professionnel de la santé visible par le patient&quot;,&quot;Label_ModePandemie_RendezVous&quot;: &quot;Rendez-vous : &quot;,&quot;Label_ModePandemie_Telephonique&quot;: &quot;Type de rendez-vous disponible : téléphonique&quot;,&quot;Label_ModePandemie_EnClinique&quot;: &quot;Type de rendez-vous disponible : en clinique&quot;,&quot;Label_userCommentsClinic&quot;: &quot;Note du patient (raison du rendez-vous)&quot;,&quot;Label_Comment&quot;: &quot;Note de la clinique&quot;,&quot;ErrorMessage_EmployeeDeactivatedWithRDVOrTimeAvail&quot;: &quot;Avant d&#39;effectuer cette opération, vous devez annuler les plages de disponibilité et les rendez-vous à venir de cet employé.&quot;,&quot;ErrorMessage_ServerError_Citoyen&quot;: &quot;Une erreur inattendue est survenue.&lt;/br&gt; Veuillez réessayer. &quot;,&quot;ErrorMessage_ServerError_part1&quot;: &quot;Un problème technique est survenu. Informez-nous en écrivant à l&#8217;adresse courriel &lt;a href=&#39;mailto:<EMAIL>&#39;&gt;<EMAIL>&lt;/a&gt;. Prenez soin d&#8217;indiquer le lien de l&#8217;erreur suivante:&lt;/br&gt; &lt;/br&gt;&quot;,&quot;Label_error&quot;: &quot;Erreur &lt;/br&gt; &lt;/br&gt;&quot;,&quot;Label_technicalPb&quot;: &quot;Problème technique &lt;/br&gt; &lt;/br&gt;&quot;,&quot;Label_poste&quot;: &quot;Poste&quot;,&quot;Info_InclusiveCriteria&quot;: &quot;Seuls les rendez-vous répondant aux critères sélectionnés seront affichés dans la liste. Consultez &lt;a href=&#39;/helpdoc/fr/#Agenda_Reports_Options&#39; target=&#39;GRDVHelp&#39;&gt;l&#8217;aide contextuelle&lt;/a&gt; pour plus de détails.&quot;,&quot;Label_error_locationMessage&quot;: &quot;La longueur du message doit être de 1 à 300 caractères.&quot;,&quot;Label_error_locationMessageLong&quot;: &quot;Veuillez inscrire un texte dans le champ « Message » de moins de 300 caractères.&quot;,&quot;Label_LocationMessageEditBoxPersonalize&quot;: &quot;Personnalisé pour tous les lieux de l&#39;entreprise&quot;,&quot;Label_locationMessageCompanyConfig&quot;: &quot;Configuré pour l&#8217;entreprise&quot;,&quot;Label_LocationMessagesLocationEnteprise&quot;: &quot;Entreprise&quot;,&quot;Label_AssureAppointmentGMF2dot2&quot;: &quot;Prendre rendez-vous avec un membre de l&#39;équipe de relais n&lt;sup&gt;o&lt;/sup&gt; 2 de mon professionnel de la santé&quot;,&quot;Label_AssureAppointmentGMF2dot3&quot;: &quot;Prendre rendez-vous avec un professionnel de la santé de mon groupe de médecine de famille (GMF)&quot;,&quot;Label_AvailableAppointmentsMF&quot;: &quot;Rendez-vous avec mon professionnel de la santé &lt;br&gt; disponibles pour « {0} » à partir du&quot;,&quot;Label_AvailableAppointmentsGMFdot1&quot;: &quot;Rendez-vous avec un membre de l&#39;équipe de relais n&lt;sup&gt;o&lt;/sup&gt; 1 de mon professionnel de la santé &lt;br&gt; disponibles pour « {0} » à partir du&quot;,&quot;Label_AvailableAppointmentsGMFdot2&quot;: &quot;Rendez-vous avec un membre de l&#39;équipe de relais n&lt;sup&gt;o&lt;/sup&gt; 2 de mon professionnel de la santé &lt;br&gt; disponibles pour « {0} » à partir du&quot;,&quot;Label_AvailableAppointmentsGMFdot3&quot;: &quot;Rendez-vous avec un professionnel de la santé de mon GMF &lt;br&gt; disponibles pour « {0} » à partir du&quot;,&quot;WarningMessage_ServiceDoesntFitInTa&quot;: &quot;Une plage de {0} ne pourra être complètement remplie avec des services de {1}&quot;,&quot;WarningMessage_ServiceDoesntFitInTaInferieur&quot;: &quot;Une plage de {0} est inférieure à la durée minimale du service. Veuillez modifier la plage horaire ou choisir un service de plus courte durée.&quot;,&quot;prof_type_doctor&quot;: &quot;Médecin&quot;,&quot;prof_type_ips&quot;: &quot;Infirmière&quot;,&quot;prof_type_resident&quot;: &quot;Résident&quot;,&quot;professional_types&quot;: &quot;Profession&quot;,&quot;Label_activity_has_passed&quot;: &quot;L&#39;heure de ce rendez-vous est passée&quot;,&quot;ErrorMessage_AppointmentTimeIsNotAvailable&quot;: &quot;Désolé, l&#39;heure de rendez-vous demandée n&#39;est plus disponible.&quot;,&quot;PROFESSIONAL_IS_ALONE_IN_A_PROFESSIONAL_GROUP&quot;: &quot;Voulez-vous vraiment supprimer la fiche de cet employé? Il sera retiré des groupes de travail et des équipes de relais dont il fait partie.&quot;,&quot;cr_out_of_date_notification&quot;: &quot;La raison de consultation « {0} » a été supprimée par la RAMQ. Elle n&#8217;est donc plus disponible dans la liste de services lors de la création ou de la modification d&#8217;une plage de disponibilité. Cette suppression n&#8217;a pas de conséquence sur les plages de disponibilité ou les rendez-vous déjà créés.&quot;,&quot;Labels_ProfessionalsFrWeb&quot;: &quot;doctor:Médecin de famille;nursing_specialized_nurse_practitioner:IPS;nursing_registered:Infirmier ou Infirmière;nursing_licensed_practical:Infirmier ou Infirmière auxiliaire;social_worker:Travailleur ou Travailleuse social;psychologist:Psychologue;dietitian_nutritionist:Diététiste &amp; Nutritionniste;physiotherapist:Physiothérapeute &amp; Thérapeute en réadaptation;occupational_therapist:Ergothérapeute;pharmacist:Pharmacien ou Pharmacienne;respiratory_therapist:Inhalothérapeute;resident1:Résident;extern:Externe;resident2:Résident;resident:Résident&quot;,&quot;Labels_ProfessionalsDisplayOrder&quot;: &quot;social_worker,psychologist,dietitian_nutritionist,physiotherapist,occupational_therapist,respiratory_therapist,nursing_registered,nursing_licensed_practical,pharmacist,doctor,nursing_specialized_nurse_practitioner,resident1,resident2,resident,extern&quot;,&quot;Labels_ProfessionalsDefinitions&quot;: &quot;nursing_registered:nurse_practitioner;nursing_licensed_practical:nurse_practitioner;nursing_specialized_nurse_practitioner:nurse_practitioner;social_worker:other_health_professional;psychologist:other_health_professional;dietitian_nutritionist:other_health_professional;physiotherapist:other_health_professional;occupational_therapist:other_health_professional;respiratory_therapist:other_health_professional&quot;,&quot;Labels_ProfessionalSpecialtyGrouping&quot;: &quot;nursing_registered:nursing_registered,infirmière_clinicienne&quot;,&quot;Labels_ProfessionalKindGrouping&quot;: &quot;doctor:doctor,generic_doctor&quot;,&quot;notif_would_destroy_prof_group_integrity&quot;: &quot;Vous ne pouvez pas supprimer cet employé, car cela occasionne un doublon dans les groupes de travail. Vous devez supprimer un groupe de travail avant de supprimer l&#8217;employé.&quot;,&quot;ErrorMessage_crDescriptionFrLengthCantBeGreater&quot;: &quot;La longueur de la description française ne peut pas dépasser 300 caractères&quot;,&quot;ErrorMessage_crMustHaveFrTitle&quot;: &quot;Inscrivez le nom en français.&quot;,&quot;ErrorMessage_crMustHaveEnTitle&quot;: &quot;Inscrivez le nom en anglais.&quot;,&quot;ErrorMessage_crMustHaveEnDescription&quot;: &quot;Inscrivez une description en anglais.&quot;,&quot;ErrorMessage_crMustHaveFrDescription&quot;: &quot;Inscrivez une description en français.&quot;,&quot;ErrorMessage_crMustHaveStartTime&quot;: &quot;Inscrivez une date de début.&quot;,&quot;ErrorMessage_crDescriptionEnLengthCantBeGreater&quot;: &quot;La longueur de la description anglais ne peut pas dépasser 300 caractères&quot;,&quot;Label_AssureAppointmentNearbyClinic_trans&quot;: {    &quot;fr&quot;: &quot;Prendre rendez-vous dans une clinique à proximité&quot;,    &quot;en&quot;: &quot;Schedule an appointment in a clinic close to me&quot;  },&quot;Label_AssureAppointmentGMF_trans&quot;: {    &quot;fr&quot;: &quot;Prendre rendez-vous avec un membre de l&#39;équipe de relais n&lt;sup&gt;o&lt;/sup&gt; 1 de mon professionnel de la santé&quot;,    &quot;en&quot;: &quot;Schedule an appointment with a member of my health professsional&#39;s relay team no. 1&quot;  },&quot;Label_ClickToContinueGMF_trans&quot;: {    &quot;fr&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,    &quot;en&quot;: &quot;Please click on the button below to look for more available time slots&quot;  },&quot;Label_ClickToContinueGEN_trans&quot;: {    &quot;fr&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,    &quot;en&quot;: &quot;Please click on the button below to look for other available time slots.&quot;  },&quot;Label_ClickToContinueGEN_NOGMF_trans&quot;: {    &quot;fr&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,    &quot;en&quot;: &quot;Please click on the button below to look for other available time slots.&quot;  },&quot;Activity_SomeTA_NotShown_trans&quot;: {    &quot;fr&quot;: &quot;Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de votre professionnel de la santé ou cliquer sur le bouton ci-dessous.&quot;,    &quot;en&quot;: &quot;Certain availabilities cannot be displayed in the Québec Medical Appointment Scheduler. Call the clinic to find out all of the availabilities of your health professional or click on the button above.&quot;  },&quot;Activity_SomeTA_NotShownGmf_trans&quot;: {    &quot;fr&quot;: &quot;Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de ses professionnels de la santé ou cliquer sur le bouton ci-dessous.&quot;,    &quot;en&quot;: &quot;Certain availabilities cannot be displayed in the Québec Medical Appointment Scheduler. Call the clinic to find out all of the availabilities of its health professionals or click on the button below to look for other available time slots.&quot;  },&quot;Activity_SomeTA_NotShownMfNoGMF_trans&quot;: {    &quot;fr&quot;: &quot;Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de ses professionnels de la santé ou cliquer sur le bouton ci-dessous.&quot;,    &quot;en&quot;: &quot;Certain availabilities cannot be displayed in the Québec Medical Appointment Scheduler. Call the clinic to find out all of the availabilities of its health professionals or click on the button below to look for other available time slots.&quot;  },&quot;Label_ClickToContinueGEN_trans&quot;: {    &quot;fr&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,    &quot;en&quot;: &quot;Please click on the button below to look for other available time slots.&quot;  },&quot;Label_ClickToContinueGMF_trans&quot;: {    &quot;fr&quot;: &quot;Cliquez sur le bouton ci-dessous pour voir plus de disponibilités&quot;,    &quot;en&quot;: &quot;Please click on the button below to look for more available time slots&quot;  },&quot;Label_ClickToContinueNoGMF_trans&quot;: {    &quot;fr&quot;: &quot;&quot;,    &quot;en&quot;: &quot;ANGLAIS - Veuillez cliquer sur le bouton ci-dessous pour voir les disponibilités offertes par les cliniques à proximité.&quot;  },&quot;Link_ContactAndHelp_trans&quot;: {    &quot;fr&quot;: &quot;Pour une aide détaillée, consultez le &lt;a href=&#39;https://rvsq.gouv.qc.ca/info/aide-cliniques-medicales.html&#39;&gt; Guide d&#8217;utilisation &#8211; Volet clinique&lt;/a&gt;, qui présente toutes les fonctionnalités de Rendez-vous santé Québec. &quot;,    &quot;en&quot;: &quot;Pour une aide détaillée, consultez le &lt;a href=&#39;https://rvsq.gouv.qc.ca/info/aide-cliniques-medicales.html&#39;&gt; Guide d&#8217;utilisation &#8211; Volet clinique&lt;/a&gt;, qui présente toutes les fonctionnalités de Rendez-vous santé Québec. &quot;  },&quot;Label_Places_Options&quot;: &quot;Label_Places_options&quot;}'));var displayPreferences = { 
timeFormat: 'HH:mm',dateFormat: 'dd/MM/yyyy'};
//]]>
</script>

        <!-- Bandeau -->
        
<div id="bandeaurvsq">
    <table id="IdBanniere" style="width: 100%">
        <tbody><tr>
            <td style="width: 30px; vertical-align: top;">
                <div style="margin: auto; padding: 0px;">
                    <svg id="warningIcon" xmlns="http://www.w3.org/2000/svg" width="22px" height="22px" fill="#8f7b27" class="bi bi-exclamation-triangle-fill" viewBox="0 0 16 16">
                        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5m.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2"></path>
                    </svg>
                    <svg id="infoIcon" xmlns="http://www.w3.org/2000/svg" width="22px" height="22px" fill="#8f7b27" class="bi bi-info-circle-fill" viewBox="0 0 16 16">
                        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zM8.93 4.58a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zM8 6.5a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-1 0v-3a.5.5 0 0 1 .5-.5zm0 4.5a.5.5 0 1 1 0 1 .5.5 0 0 1 0-1z"></path>
                    </svg>
                </div>
            </td>
            <td>
                <div style="margin: auto; padding: 0px;">
                    <span id="messagebandeaurvsq"></span>
                </div>
            </td>
            <td>
                <div class="align-items-center divfermerbandeau">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#314359" class="fermerbandeau" onclick="Common.FermerBandeau()">
                        <path d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path>
                    </svg>
                </div>
            </td>
        </tr>
    </tbody></table>
</div>

        <header class="backgr_bleu">
            <div class="container">
                <div class="piv">
                    <div><a href="../Accueil/index.html"><img src="../Images/quebec_blanc.png" alt="Aller à la page d'accueil de RVSQ."></a><div class="rdv_petit_ecran">Rendez-vous santé</div></div>
                    <div class="rdv">Rendez-vous santé</div>
                    <div class="english-container">
                        <div class="en"><a href="https://rvsq.gouv.qc.ca/prendrerendezvous/Recherche.aspx?culture=en" id="ctl00_HLLang"><span lang="en">English</span></a></div>
                        <div class="joindre"><a href="../Accueil/nousjoindre.html">Nous joindre</a></div>
                    </div>
                </div>
            </div>
        </header>


        
        <div class="container">
            <div class="row-fluid">
                <div class="span12 Assuree-ContWrap">
                    
    <div class="h-AppointmentBookingWidget">
        <div class="row-fluid">
            <div role="region" aria-label="Identification de la personne assurée" class="pull-right">
                <span><strong>Bienvenue </strong></span>   <span id="AssureName">MANU JETTE</span>
            </div>
        </div>

        <div role="banner" aria-label="Rendez-vous santé Québec" class="row-fluid">
            <div class="span12  Assuree-TitlePage">
                <div class="pull-left">
                    <span class="titre-solution"><em>Rendez-vous santé Québec</em></span>
                </div>
            </div>
        </div>

        <div class="row-fluid DoctorInfoHeader" style="display: none">
            <div class="span12 Assuree-TitleDetails">
                <div class="pull-left">
                    <i class="fa fa-user-md icon" aria-hidden="true"></i>
                </div>
                <div id="EmployeeName" class="label-icon"></div>
            </div>
        </div>

        <div class="row-fluid ClinicInfoHeader" style="">
            <div class="span12 Assuree-ClinicDetails">
                <div id="ClinicInfo"><div class="pull-left icon"><div>&nbsp;&nbsp;&nbsp;</div></div><div class="label-icon"><strong>CENTRE MÉDICAL MIEUX-ÊTRE, SUCCURSALE ST-LAURENT</strong><br> 400 Av. Sainte-Croix<br>Montréal, QC, H4N 3L4<br>************ </div></div>
            </div>
        </div>


        <div class="row-fluid backwrapper" style="">
            <div class="span12 pull-left">
                <a id="AppointmentBookingBackButton" class="h-BackButton backButton btn withArrow">Retour </a>
                <a id="AppointmentBookingRestartButton" class="btn restartButton h-restartButton" style="">Recommencer     <span class="fa fa-undo h-restartButton" aria-hidden="true" style=""></span></a>
                
            </div>
        </div>

        <div role="main" class="h-View h-AssureLanding" data-nav-en="home" data-nav-fr="accueil" data-context="AssureLanding" style="display: none;">
            

<div>
    
        <div class="Assure form well well-small well-custom">
          
            <div class="alert WarningMessage_ExpiredNAM" style="display: none;">
                <i class="customAlert fa fa-exclamation-triangle" aria-hidden="true"></i>
                <span class="WarningImgAssure focusOnShow">Attention</span>
                <label class="control-label customAlert" for="AssureForm_ErrorMessage">Nos dossiers indiquent que votre situation doit être régularisée auprès de la RAMQ. Communiquez avec nous au **************. Nos bureaux sont ouverts de 8:30 à 16:30 le lundi, le mardi, le jeudi et le vendredi, et de 10:00 à 16:30 le mercredi. Vous pouvez quand même prendre rendez-vous à l’aide du service en ligne, mais la clinique se réserve le droit de refuser ou de vous facturer votre consultation si votre situation n’a pas changé au moment de votre rendez-vous.</label>
            </div>
            <div class="alert WarningMessage_NoMd" style="display: block;">
                <i class="customAlert fa fa-exclamation-triangle" aria-hidden="true"></i>
                <span class="WarningImgAssure focusOnShow">Attention</span>
                <label class="control-label customAlert" for="AssureForm_ErrorMessage">Selon nos renseignements, vous n'avez pas de médecin de famille. Vous pouvez vous inscrire sur la liste d'attente par le biais du <a href="http://www.gamf.gouv.qc.ca/" target="_blank">Guichet d'accès à un médecin de famille&nbsp;<img src="/images/lien_externe.gif" alt="Cet hyperlien s’ouvrira dans une nouvelle fenêtre." width="9" height="9"></a>. Vous pouvez aussi prendre rendez-vous avec un professionnel de la santé dans une clinique à proximité. </label>
            </div>
            <div class="alert alert-error ErrorMessage_Pilot" style="display: none;">
                <i class="fa fa-info-circle" aria-hidden="true"></i>
                <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                <label class="control-label" for="AssureForm_ErrorMessage"><p>Nous sommes désolés, mais vous n’avez pas accès à Rendez-vous santé Québec pour le moment.</p><p>Le nouveau service fait présentement l’objet d’un déploiement progressif. Seules les personnes qui résident dans la ou les régions visées ou qui ont un professionnel de la santé dans l’une des <a href="https://rvsq.gouv.qc.ca/accueil/index.html" target="_blank">cliniques participantes</a> peuvent prendre rendez-vous en ligne.</p><p>Vous pourrez utiliser le service dans les prochains mois.</p></label>
            </div>

            <div class="urgent_container alert insured-alert__container">
                <p>
                    <b>Pour toute urgence vitale, joignez les services d’urgence au 911.</b>
                </p>
               <p>
                    Pour vous guider, vous pouvez aussi composer le 811 (Info-Santé et Info-Social).
               </p> 
            </div>

            <div class="h-SubView h-SelectAssureList1" style="display: none">
                
                    <div class="h-ViewContent content centered">
                        <ul class="SelectAssureList1 thumbnails">
                        </ul>
                    </div>
                
            </div>

            <div class="h-SubView h-SelectAssureList2" style="display: none">
                
                    <div class="h-ViewContent content centered">
                        <ul class="SelectAssureList2 thumbnails">
                        </ul>
                    </div>
                
            </div>

             <div class="h-SubView h-SelectAssureList3" style="">
                
                    <div class="h-ViewContent content centered">
                        <ul class="SelectAssureList3 thumbnails"><li class="span6"><a class="h-SelectAssureBtn ctx-changer" href="#" data-type="3"><div class="thumbnail tmbArrow">Prendre rendez-vous dans une clinique à proximité</div> </a></li><li class="span6"><a class="h-ModifyCancelBtn" href="#"><div class="thumbnail tmbArrow">Mettre à jour mes coordonnées ou annuler mon rendez-vous</div> </a></li></ul>
                    </div>
                
            </div>
           
        </div>
           
            <br>

        <input type="hidden" id="selectedAppointmentType">
           
  

    <div class="modal hide fade" tabindex="-1" role="dialog" id="inscritGroupeMedecinsPopup">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
		
            <div class="modal-header">                
                <button type="button" class="close" data-dismiss="modal">×</button>
                <h4 class="modal-title">Obtenir une consultation avec un professionnel de la santé</h4>
            </div>

            <div class="modal-body">
                <p>
                    Comme vous êtes inscrit auprès d’un groupe de médecins, vous pouvez obtenir une consultation ou un service médical en utilisant le 
                    <a href="https://www.quebec.ca/sante/trouver-une-ressource/guichet-acces-premiere-ligne" target="_blank">
                        Guichet d’accès à la première ligne (GAP)
                        <img src="/images/lien_externe.gif" alt="Cet hyperlien s’ouvrira dans une nouvelle fenêtre." width="9" height="9">
                    </a>
                </p>
            </div>
			
            <div class="modal-footer text-center">
                <button type="button" class="btn  btn-primary h-CloseButton" data-dismiss="modal">Fermer</button>
            </div>
			
        </div>
    </div>
</div>

    </div>


        </div>

        <div class="h-View h-Search" data-nav-en="criteria" data-nav-fr="criteres" data-context="Search" style="display: none;">
            <div class="row-fluid">

                <div class="span12">
                    <h1 class="titre-page h-search-titre-page" tabindex="0">Prendre rendez-vous dans une clinique à proximité</h1>
                    <p class="sr-only">L'information de cette page peut être mise à jour sans rechargement.</p>  
                    <div class="alert WarningMessage_RelationMDEnding" style="display: none">
                        <i class="customAlert fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <span class="WarningImgAssure focusOnShow">Attention</span>
                        <label class="control-label customAlert" for="AssureForm_ErrorMessage">Selon nos informations, vous n’aurez plus de professionnel de la santé à compter du {0}. Pour plus de détails, veuillez communiquer avec votre clinique.</label>
                    </div>

                    <div class="alert WarningMessage_InscrGMFEnding" style="display: none">
                        <i class="customAlert fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <span class="WarningImgAssure focusOnShow">Attention</span>
                        <label class="control-label customAlert" for="AssureForm_ErrorMessage">Selon nos informations, à compter du {0}, des changements sont prévus à votre clinique. Pour plus de détails, veuillez communiquer avec celle-ci.</label>
                    </div>

                    <div class="alert WarningMessage_InscrFollowLocEnding" style="display: none">
                        <i class="customAlert fa fa-exclamation-triangle" aria-hidden="true"></i>
                        <span class="WarningImgAssure focusOnShow">Attention</span>
                        <label class="control-label customAlert" for="AssureForm_ErrorMessage">Selon nos informations, à compter du {0}, des changements sont prévus à votre clinique. Pour plus de détails, veuillez communiquer avec celle-ci.</label>
                    </div>

                    <div class="Server_ErrorMessage" style="display: none;">
                        <div class="alert alert-error">
                            <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                            <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                            <label class="control-label" for="AssureForm_ErrorMessage"></label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid">
                <div class="span5 noLeft-margin">
                    <label for="DateRangeStart" class="fieldLabel">À partir de cette date</label>
                    <div class="input-daterange input-group h-DateRange">
                        <input id="DateRangeStart" type="tel" placeholder="jj-mm-aaaa" class="input-small h-DateRangeStart span6 calendarFix findOnKeyPress sizeInput" data-standard-date-format="" data-date-format="">
                    </div>

                    <div class="type3Parameter" style="">
                        <label for="PostalCode" class="postalCode">Votre code postal</label>
                        <i tabindex="0" id="AssureRechercheForm_PostalCodeTooltip" class="fa fa-info-circle h-ToolTip input-focus__no-outline" role="button" data-toggle="popover" data-placement="right" data-html="true" data-trigger="focus" data-content="Inscrivez votre code postal ou tout autre code postal près duquel vous souhaitez trouver une clinique." data-original-title=""></i>
                        <input aria-label="Votre code postal" data-mask="S0S 0S0" data-majus="true" type="text" class="span6 findOnKeyPress sizeInput" id="PostalCode" maxlength="7" autocomplete="off">

                        <label for="perimeterCombo">Périmètre de recherche (km)</label>
                        <i tabindex="0" id="AssureRechercheForm_PerimeterTooltip" class="fa fa-info-circle h-ToolTip input-focus__no-outline" role="button" data-toggle="popover" data-placement="right" data-html="true" data-trigger="focus" data-content="Le périmètre de recherche par code postal se fait à vol d’oiseau, c’est-à-dire qu’il ne tient pas compte du réseau routier. La distance réelle avec le réseau routier peut donc être plus grande." style="cursor:help" data-original-title=""></i>
                        <br><select name="perimeterCombo" id="perimeterCombo">
                        <option value="0">10 km</option><option value="1">20 km</option><option value="2">30 km</option><option value="3">40 km</option><option value="4">50 km</option></select>
                    </div>
                </div>
                <div class="span3">
                    <div class="well well2 fix-moment-journee">
                        <fieldset>
                            <legend class="legend1em">Moment de la journée</legend>
                            <input aria-label="Avant-midi" type="checkbox" value="matin" id="chkMatin" checked="checked"><label class="inline" for="chkMatin">Avant-midi</label><br>
                            <input aria-label="Après-midi" type="checkbox" value="matin" id="chkPm" checked="checked"><label class="inline" for="chkPm">Après-midi</label><br>
                            <input aria-label="Soir" type="checkbox" value="matin" id="chkSoir" checked="checked"><label class="inline" for="chkSoir">Soir</label>
                        </fieldset>
                    </div>
                </div>
            </div>

            <div class="row-fluid consulting-reason__container">
                <div class="span12 noLeft-margin">
                    <h2>Service</h2>
                    <p>Pour assurer le bon fonctionnement du service de prise de rendez-vous, veuillez choisir la raison de consultation appropriée à votre situation.</p>
                </div>
            </div>

            <div class="row-fluid consulting-reason__container">
                <div class="span5">
                    <select name="consultingReason" id="consultingReason" class="h-consultingReason">
                        <option value=" ">Choisir une raison</option>
                    <option value="ac2544d2-8514-11ef-a759-005056b11d6c">Consultation semi-urgente</option><option value="ac2a5fa4-8514-11ef-a759-005056b11d6c">Consultation urgente</option><option value="ac5e54ee-8514-11ef-a759-005056b11d6c">Suivi</option><option value="ac64a4e3-8514-11ef-a759-005056b11d6c">Suivi de grossesse</option><option value="ac6a6f25-8514-11ef-a759-005056b11d6c">Suivi pédiatrique</option></select>
                </div>
                <div class="span6">
                    <div class="well well2 yolo">  
                        <legend id="consultingReasonTitle" class="legend1em">Description de la raison choisie</legend>
                        <p id="consultingReasonDescription">Problème de santé récemment apparu ou aggravation d’un problème de santé existant qui nécessite une consultation médicale dans les <b>24 h à 48 h</b> (ex. : fièvre persistante, infection urinaire, grippe, mal de gorge aigu, vaginite, coupure nécessitant des points de suture). Pour les patients de tout âge.</p>
                    </div>
                </div>
            </div>

            <div class="row-fluid">
                <div class="span12 text-center topMargin5Pourcent">
                    <button class="h-SearchButton btn btn-primary" type="button">Rechercher</button>
                    <div id="msg-new-results" class="sr-only" aria-hidden="false">De nouveaux résultats se sont affichés.</div>
                </div>
            </div>

            <div class="h-ClinicSelection row-fluid" style="">
                <div class="content centered">
                    <h1 class="titre-page focusOnShow GMF" id="clinicsWithDisponibilities">Les cliniques suivantes offrent des disponibilités pour votre rendez-vous&nbsp;:</h1>
                    <div class="alert" id="clinicsWithNoDisponibilitiesContainer" style="margin-top: 8px; display: none;">
                        <p id="clinicsWithNoDisponibilities">Toutes les plages de disponibilité de votre professionnel de la santé offertes en ligne ont été comblées.</p>
                        <ol class="steps bold errorMessageResultNotFoundOl" id="clinicsWithNoDisponibilitiesCovid" style="display: none;">
                            <li>Pour trouver une autre ressource qui offre de la consultation le jour même ou dans les jours suivants, consultez la page 
                                <a href="https://sante.gouv.qc.ca/repertoire-ressources/consultations-medicales-sans-rendez-vous/" target="_blank">Connaître les cliniques qui offrent des consultations médicales le jour même ou les jours suivants</a>.
                                <img src="/images/lien_externe.gif" alt="Cet hyperlien s'ouvrira dans une nouvelle fenêtre." width="9" height="9">
                            </li>
                        </ol>
                    </div>
                    <div class="visible-small-devices" id="tooltip-legend" style="display: none;">
                        <p><i class="fa fa-star gold"></i>  Votre lieu de suivi</p>
                    </div>
                    <div class="searchResultMessage" style="display: none;"></div>
            <div class="h-ClinicSelection row-fluid" style="">
                <div class="content centered">
                    <div class="searchResultMessage" style="display: none;"></div>
                    <div class="h-TANotShownSearch" style="display: none;">
                        <p>Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de votre professionnel de la santé ou cliquer sur le bouton ci-dessous.</p>
                        <p></p>
                    </div>
                    <div class="otherTypeActions" style="display: none">
                        <ul>
                            <li class="span6 butType3">
                                <a href="javascript:;" target="_blank">
                                    <div class="thumbnail tmbArrow tmbBtn h-butType3">Prendre rendez-vous dans une clinique à proximité </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="span10 nPaging center center-paging" style="display: none;"></div>
                </div>
            </div>
                    <div class="otherTypeActions" style="display: none">
                        <ul class="thumbnails">
                            <li class="span6 butType2dot2">
                                <a href="javascript:;" target="_blank">
                                    <div class="thumbnail tmbArrow tmbBtn h-butType2dot2">Prendre rendez-vous avec un professionnel de la santé de mon groupe de médecine de famille (GMF)</div>
                                </a>
                            </li>
                            <li class="span6 butType2">
                                <a href="javascript:;" target="_blank">
                                    <div class="thumbnail tmbArrow tmbBtn h-butType2">Prendre rendez-vous avec un membre de l'équipe de relais n<sup>o</sup> 1 de mon professionnel de la santé </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="h-ResultsContainer container" style="">
                        <div class="row">
                        <div class="h-ResultsItemContainer span6">
                            <ul class="ClinicList thumbnails h-ClinicList" id="ClinicList" data-itemlist_clinique="&lt;li class='span6 animated bounceIn' style='width:100%'&gt;    &lt;a class='h-selectClinic' href='javascript:;' data-CompanyId={0} data-ProfessionalId={1} data-CompanyAdressId={2} data-isPetalLocation={3} data-categoryPetal={4} data-isremindersmsenabled={5} data-startDate={6} data-isreminderphoneenabled={7}&gt;        &lt;div class='thumbnail tmbArrow tmbClinic'&gt;            &lt;div&gt;                &lt;div style='float:left'&gt;                    &lt;img src='/Images/MapsMarker/place-{8}.png' alt='Voir carte'&gt;                &lt;/div&gt;            &lt;/div&gt;            {9}            &lt;div class='tmbWrapper' style='float:left'&gt;                &lt;h2 class='remove-margin clinic-title'&gt;{10}&lt;/h2&gt;                &lt;p&gt; {11} {12}{13}{14} &lt;br/&gt;{15}, {16} {17} &lt;br/&gt;{18} &lt;/p&gt;                &lt;p&gt; {19} &lt;/p&gt;            &lt;/div&gt;            {20}            &lt;div style='clear:both'&gt;&lt;/div&gt;&lt;hr class='remove-margin'/&gt;                &lt;div class='text-right tmbDate'&gt;                    {21} &nbsp;&lt;span class='gold'&gt;{22} {23} {24}&lt;/span&gt;                &lt;/div&gt;                &lt;div class='text-right tmbDate'&gt;                    &lt;strong&gt; {25} &lt;/strong&gt;                &lt;/div&gt;            &lt;/div&gt;        &lt;/div&gt;    &lt;/a&gt;&lt;/li&gt;" data-itemlist_clinique-distance="&lt;div class='tmbWrapper' style='float:right'&gt;    &lt;p&gt; {0} km&lt;/p&gt;&lt;/div &gt;" data-itemlist_clinique_goldstar="&lt;div style='float:left'&gt;    &lt;span id='AssureRecherche_YourClinicTooltip' class='fa fa-star fa-lg gold starToolTip' aria-label='{0}' aria-hidden='false' data-toggle='tooltip' data-placement='right' data-html='true' title='&lt;p align=@apos;left@apos;&gt;{1}&lt;/p&gt;'&gt;&lt;/span&gt;&lt;/div&gt;" style=""><li class="span6 animated bounceIn" style="width:100%">    <a class="h-selectClinic" href="javascript:;" data-companyid="6014" data-professionalid="0" data-companyadressid="6014" data-ispetallocation="true" data-categorypetal="0" data-isremindersmsenabled="true" data-startdate="2025-06-12T08:00:00-04:00" data-isreminderphoneenabled="true">        <div class="thumbnail tmbArrow tmbClinic">            <div>                <div style="float:left">                    <img src="/Images/MapsMarker/place-01.png" alt="Voir carte">                </div>            </div>                        <div class="tmbWrapper" style="float:left">                <h2 class="remove-margin clinic-title">La Clinique Pour Enfants Pointe-Claire</h2>                <p>  955 Boul. Saint-Jean, Tour 2, Bureau 311 <br>Pointe-Claire, QC H9R 4Y2 <br>************ </p>                <p> &nbsp; </p>            </div>            <div class="tmbWrapper" style="float:right">    <p> 20,02 km</p></div>            <div style="clear:both"></div><hr class="remove-margin">                <div class="text-right tmbDate">                    Première date disponible : &nbsp;<span class="gold">12 <abbr title="juin">juin</abbr>  2025</span>                </div>                <div class="text-right tmbDate">                    <strong> Type de rendez-vous disponible : en clinique </strong>                </div>            </div>            </a></li><li class="span6 animated bounceIn" style="width:100%">    <a class="h-selectClinic" href="javascript:;" data-companyid="13022" data-professionalid="9539" data-companyadressid="13022" data-ispetallocation="true" data-categorypetal="0" data-isremindersmsenabled="true" data-startdate="2025-06-12T13:20:00-04:00" data-isreminderphoneenabled="true">        <div class="thumbnail tmbArrow tmbClinic">            <div>                <div style="float:left">                    <img src="/Images/MapsMarker/place-02.png" alt="Voir carte">                </div>            </div>                        <div class="tmbWrapper" style="float:left">                <h2 class="remove-margin clinic-title">CENTRE PÉDIATRIQUE LUMI</h2>                <p>  3883 Boul Saint-Jean,  suite 215 <br>Dollard-des-Ormeaux, QC H9G 3B9 <br>************ </p>                <p> &nbsp; </p>            </div>            <div class="tmbWrapper" style="float:right">    <p> 20,04 km</p></div>            <div style="clear:both"></div><hr class="remove-margin">                <div class="text-right tmbDate">                    Première date disponible : &nbsp;<span class="gold">12 <abbr title="juin">juin</abbr>  2025</span>                </div>                <div class="text-right tmbDate">                    <strong> Type de rendez-vous disponible : en clinique </strong>                </div>            </div>            </a></li><li class="span6 animated bounceIn" style="width:100%">    <a class="h-selectClinic" href="javascript:;" data-companyid="8926" data-professionalid="0" data-companyadressid="8926" data-ispetallocation="true" data-categorypetal="0" data-isremindersmsenabled="true" data-startdate="2025-06-12T13:30:00-04:00" data-isreminderphoneenabled="true">        <div class="thumbnail tmbArrow tmbClinic">            <div>                <div style="float:left">                    <img src="/Images/MapsMarker/place-03.png" alt="Voir carte">                </div>            </div>                        <div class="tmbWrapper" style="float:left">                <h2 class="remove-margin clinic-title">CENTRE MÉDICAL MIEUX-ÊTRE, SUCCURSALE ST-LAURENT</h2>                <p>  400 Av. Sainte-Croix <br>Montréal, QC H4N 3L4 <br>************ </p>                <p> &nbsp; </p>            </div>            <div class="tmbWrapper" style="float:right">    <p> 6,80 km</p></div>            <div style="clear:both"></div><hr class="remove-margin">                <div class="text-right tmbDate">                    Première date disponible : &nbsp;<span class="gold">12 <abbr title="juin">juin</abbr>  2025</span>                </div>                <div class="text-right tmbDate">                    <strong> Type de rendez-vous disponible : en clinique </strong>                </div>            </div>            </a></li><li class="span6 animated bounceIn" style="width:100%">    <a class="h-selectClinic" href="javascript:;" data-companyid="13028" data-professionalid="30189" data-companyadressid="13028" data-ispetallocation="true" data-categorypetal="0" data-isremindersmsenabled="true" data-startdate="2025-06-12T10:30:00-04:00" data-isreminderphoneenabled="true">        <div class="thumbnail tmbArrow tmbClinic">            <div>                <div style="float:left">                    <img src="/Images/MapsMarker/place-04.png" alt="Voir carte">                </div>            </div>                        <div class="tmbWrapper" style="float:left">                <h2 class="remove-margin clinic-title">Clinique Médicale Step</h2>                <p>  5757 Boul Cavendish, Suite 300 <br>Côte Saint-Luc, QC H4W 2W8 <br>************ </p>                <p> &nbsp; </p>            </div>            <div class="tmbWrapper" style="float:right">    <p> 7,76 km</p></div>            <div style="clear:both"></div><hr class="remove-margin">                <div class="text-right tmbDate">                    Première date disponible : &nbsp;<span class="gold">12 <abbr title="juin">juin</abbr>  2025</span>                </div>                <div class="text-right tmbDate">                    <strong> Type de rendez-vous disponible : en clinique </strong>                </div>            </div>            </a></li></ul>
                        </div>
                        
                        <div id="h-MapsContainer" class="h-MapsContainer span6" style=""><div class="h-MapsContainerMap leaflet-container leaflet-retina leaflet-fade-anim leaflet-grab leaflet-touch-drag" id="h-MapsContainerMap" tabindex="0" style="position: relative;"><div class="leaflet-pane leaflet-map-pane" style="transform: translate3d(0px, 0px, 0px);"><div class="leaflet-pane leaflet-tile-pane"><div class="leaflet-layer " style="z-index: 1; opacity: 1;"><div class="leaflet-tile-container leaflet-zoom-animated" style="z-index: 20; transform: translate3d(0px, 0px, 0px) scale(1);"><img alt="" role="presentation" src="https://mt0.google.com/vt/lyrs=m&amp;x=9&amp;y=11&amp;z=5" class="leaflet-tile leaflet-tile-loaded" style="width: 256px; height: 256px; transform: translate3d(-115px, -115px, 0px); opacity: 1;"></div></div></div><div class="leaflet-pane leaflet-shadow-pane"><img src="/Images/MapsMarker/marker-shadow.png" class="leaflet-marker-shadow leaflet-zoom-animated" alt="" style="margin-left: -12px; margin-top: -41px; width: 41px; height: 41px; transform: translate3d(-3px, 1px, 0px);"><img src="/Images/MapsMarker/marker-shadow.png" class="leaflet-marker-shadow leaflet-zoom-animated" alt="" style="margin-left: -12px; margin-top: -41px; width: 41px; height: 41px; transform: translate3d(-3px, 0px, 0px);"><img src="/Images/MapsMarker/marker-shadow.png" class="leaflet-marker-shadow leaflet-zoom-animated" alt="" style="margin-left: -12px; margin-top: -41px; width: 41px; height: 41px; transform: translate3d(1px, -1px, 0px);"><img src="/Images/MapsMarker/marker-shadow.png" class="leaflet-marker-shadow leaflet-zoom-animated" alt="" style="margin-left: -12px; margin-top: -41px; width: 41px; height: 41px; transform: translate3d(1px, 0px, 0px);"><img src="/Images/MapsMarker/marker-shadow.png" class="leaflet-marker-shadow leaflet-zoom-animated" alt="" style="margin-left: -12px; margin-top: -41px; width: 41px; height: 41px; transform: translate3d(3px, -1px, 0px);"></div><div class="leaflet-pane leaflet-overlay-pane"></div><div class="leaflet-pane leaflet-marker-pane"><img src="/Images/MapsMarker/place-01.png" class="leaflet-marker-icon leaflet-zoom-animated leaflet-interactive" alt="" tabindex="0" style="margin-left: -12px; margin-top: -41px; width: 25px; height: 41px; transform: translate3d(-3px, 1px, 0px); z-index: 1;"><img src="/Images/MapsMarker/place-02.png" class="leaflet-marker-icon leaflet-zoom-animated leaflet-interactive" alt="" tabindex="0" style="margin-left: -12px; margin-top: -41px; width: 25px; height: 41px; transform: translate3d(-3px, 0px, 0px); z-index: 0;"><img src="/Images/MapsMarker/place-03.png" class="leaflet-marker-icon leaflet-zoom-animated leaflet-interactive" alt="" tabindex="0" style="margin-left: -12px; margin-top: -41px; width: 25px; height: 41px; transform: translate3d(1px, -1px, 0px); z-index: -1;"><img src="/Images/MapsMarker/place-04.png" class="leaflet-marker-icon leaflet-zoom-animated leaflet-interactive" alt="" tabindex="0" style="margin-left: -12px; margin-top: -41px; width: 25px; height: 41px; transform: translate3d(1px, 0px, 0px); z-index: 0;"><img src="/Images/MapsMarker/place-depart.png" class="leaflet-marker-icon leaflet-zoom-animated leaflet-interactive" alt="" tabindex="0" style="margin-left: -12px; margin-top: -41px; width: 25px; height: 41px; transform: translate3d(3px, -1px, 0px); z-index: -1;"></div><div class="leaflet-pane leaflet-tooltip-pane"></div><div class="leaflet-pane leaflet-popup-pane"></div><div class="leaflet-proxy leaflet-zoom-animated" style="transform: translate3d(2418.66px, 2930.89px, 0px) scale(16);"></div></div><div class="leaflet-control-container"><div class="leaflet-top leaflet-left"><div class="leaflet-control-zoom leaflet-bar leaflet-control"><a class="leaflet-control-zoom-in" href="#" title="Zoom in" role="button" aria-label="Zoom in">+</a><a class="leaflet-control-zoom-out leaflet-disabled" href="#" title="Zoom out" role="button" aria-label="Zoom out">−</a></div></div><div class="leaflet-top leaflet-right"></div><div class="leaflet-bottom leaflet-left"></div><div class="leaflet-bottom leaflet-right"><div class="leaflet-control-attribution leaflet-control"><a href="https://leafletjs.com" title="A JS library for interactive maps">Leaflet</a> | Données cartographiques  © Google</div></div></div></div></div>
                       </div>
                    </div>
                   
                    <div class="span10 nPaging center center-paging" style="display: none;"></div>
                </div>
            </div>

            <div class="h-ClinicSelectionPartners row-fluid" style="display: none;">
                    <div class="content centered">
                        <h1 class="titre-page-partners focusOnShow" id="clinicsWithDisponibilities">Cliniques partenaires offrant aussi des disponibilités&nbsp;:</h1>
                        <div class="otherTypeActions" style="display: none">
                            <ul class="thumbnails">
                                <li class="span6 butType2dot2">
                                    <a href="javascript:;" target="_blank">
                                        <div class="thumbnail tmbArrow tmbBtn h-butType2dot2">Prendre rendez-vous avec un professionnel de la santé de mon groupe de médecine de famille (GMF)</div>
                                    </a>
                                </li>
                                <li class="span6 butType2">
                                    <a href="javascript:;" target="_blank">
                                        <div class="thumbnail tmbArrow tmbBtn h-butType2">Prendre rendez-vous avec un membre de l'équipe de relais n<sup>o</sup> 1 de mon professionnel de la santé </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
    
                        <ul class="PartnersClinicList thumbnails" id="ClinicListPartners">
                        </ul>
                        <div class="span10 nPagingPartners center center-paging"></div>
                    </div>
                </div>
           


        </div>

        <div class="h-View appointmentSelection h-AppointmentSelection" data-nav-en="appt-time-selection" data-nav-fr="selection-heure" data-context="AppointmentSelection" style="">
            <div class="container-fluid">
                <div class="h-ViewContent content">
                    <h1 id="availableAppointments" class="center titre-page focusOnShow"> <span class="selectedDate">Rendez-vous disponibles pour « consultation urgente » à partir du 12&nbsp;juin&nbsp;2025</span></h1>
                    <p class="sr-only">L'information de cette page peut être mise à jour sans rechargement.</p> 
                    <div class="h-ServerError" style="display: none;">
                        <div class="alert alert-error">
                            <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                            <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                            <label class="control-label" for="ClientInformationForm_ErrorMessage"></label>
                        </div>
                    </div>

                  

                    <div class="clear"></div>

                    <div class="well well-small topMargin10 scroll well-custom">
                        <table class="calendar h-Calendar"><thead><tr><th class="header date weekday "><abbr title="Jeudi">Jeu.</abbr>&nbsp;12 <abbr title="juin">juin</abbr></th><th class="header date weekday "><abbr title="Vendredi">Ven.</abbr>&nbsp;13 <abbr title="juin">juin</abbr></th><th class="header date weekend "><abbr title="Samedi">Sam.</abbr>&nbsp;14 <abbr title="juin">juin</abbr></th></tr></thead><tbody><tr><td class="h-Day" data-date="1749700800000"><div><button class="h-TimeButton timeButton btn btn-block" data-ids="665435028_0" style="text-shadow: none!important;background-image: none!important;background-color: #08C!important;color: #fff!important;" title="Jeudi 12 juin 2025 13:15 est disponible" data-time="1749748500000" data-tz="EDT" data-professionalid="0" data-isips="false" aria-label="Le rendez-vous de 13:15 est disponible.">13:15  </button></div><div><button class="h-TimeButton timeButton btn btn-block" data-ids="665435037_0" style="text-shadow: none!important;background-image: none!important;background-color: #08C!important;color: #fff!important;" title="Jeudi 12 juin 2025 13:30 est disponible" data-time="1749749400000" data-tz="EDT" data-professionalid="0" data-isips="false" aria-label="Le rendez-vous de 13:30 est disponible.">13:30  </button></div><div><button class="h-TimeButton timeButton btn btn-block" data-ids="665435031_0" style="text-shadow: none!important;background-image: none!important;background-color: #08C!important;color: #fff!important;" title="Jeudi 12 juin 2025 13:45 est disponible" data-time="1749750300000" data-tz="EDT" data-professionalid="0" data-isips="false" aria-label="Le rendez-vous de 13:45 est disponible.">13:45  </button></div><div><button class="h-TimeButton timeButton btn btn-block" data-ids="665435217_0" style="text-shadow: none!important;background-image: none!important;background-color: #08C!important;color: #fff!important;" title="Jeudi 12 juin 2025 15:15 est disponible" data-time="1749755700000" data-tz="EDT" data-professionalid="0" data-isips="false" aria-label="Le rendez-vous de 15:15 est disponible.">15:15  </button></div></td><td class="h-Day" data-date="1749787200000"></td><td class="h-Day" data-date="1749873600000"></td></tr></tbody></table>
                    </div>
                    <div class="availabilitiesInfo" style="display: none;"><p><i class="fa-solid fa-circle-info"></i> Cette plage est offerte par un infirmier ou une infirmière praticienne spécialisée en soins de première ligne.</p></div>
                    <div class="navigationMenu btn-toolbar center">
                        <button id="assure-prev-btn" class="btn h-PreviousCalendarPage btn-default2" title="Semaine précédente" disabled="disabled">&nbsp;<i class="fa fa-arrow-left"></i>&nbsp;<span class="hide">Semaine précédente</span></button>
                        <button id="assure-next-btn" class="btn h-NextCalendarPage btn-default2" title="Semaine suivante">&nbsp;<i class="fa fa-arrow-right"></i>&nbsp;<span class="hide">Semaine suivante</span></button>
                    </div>
                    <div class="furtherActions">
                        <p class="not_suitable">Les rendez-vous proposés ne vous conviennent pas?</p>
                        <p class="find_resource">Trouvez une ressource qui offre de la consultation médicale le jour même ou le lendemain sur le <a href="https://sante.gouv.qc.ca/repertoire-ressources/consultations-medicales-sans-rendez-vous/" target="_blank">Répertoire des ressources en santé et services sociaux</a></p>
                        <div class="alert alert-warning h-CustomMessage" style="display: none;">
                            <p></p>
                            <p></p>
                        </div>
                        <div class="h-TANotShown" style="display: none;">
                            <p>Certaines disponibilités peuvent ne pas être affichées dans Rendez-vous santé Québec. Vous pouvez téléphoner à la clinique pour connaître toutes les disponibilités de votre professionnel de la santé ou cliquer sur le bouton ci-dessous.</p>
                            <p></p>
                        </div>
                        <ul class="thumbnails">
                            <li class="span6 butType2" style="display: none;">
                                <a href="javascript:;" target="_blank">
                                    <div class="thumbnail tmbArrow tmbBtn h-butType2">Prendre rendez-vous avec un membre de l'équipe de relais n<sup>o</sup> 1 de mon professionnel de la santé </div>
                                </a>
                            </li>
                            <li class="span6 butType2dot1" style="display: none;">
                                <a href="javascript:;" target="_blank">
                                    <div class="thumbnail tmbArrow tmbBtn h-butType2dot1">Prendre rendez-vous avec un membre de l'équipe de relais n<sup>o</sup> 2 de mon professionnel de la santé</div>
                                </a>
                            </li>

                            <li class="span6 butType2dot2" style="display: none;">
                                <a href="javascript:;" target="_blank">
                                    <div class="thumbnail tmbArrow tmbBtn h-butType2dot2">Prendre rendez-vous avec un professionnel de la santé de mon groupe de médecine de famille (GMF)</div>
                                </a>
                            </li>
                            <li class="span6 butType3" style="display: none;"><a href="javascript:;" target="_blank">
                                <div class="thumbnail tmbArrow tmbBtn h-butType3">Prendre rendez-vous dans une clinique à proximité </div>
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="h-View h-AppointmentChoices appointmentChoices" data-nav-en="doctor-choice" data-nav-fr="choix-medecin" data-context="AppointmentChoices" style="display: none;">
            <div class="container-fluid">
                <div class="h-ServerError" style="display: none;">
                        <div class="alert alert-error">
                            <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                            <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                            <label class="control-label" for="ClientInformationForm_ErrorMessage"></label>
                        </div>
                    </div>
                <div id="bookingTimeLabel"></div>
                <h2>Avec quel professionnel de la santé? </h2>
                <ul class="AppointmentChoicesList thumbnails"></ul>
            </div>
        </div>

        <div class="h-View h-ClientInformationForm" data-nav-en="your-information" data-nav-fr="vos-informations" data-context="ClientInformationForm" style="display: none;">
            <div class="content centered" id="ciForm">
                

<div class="clientInformationForm">
    <div class="row-fluid">
        <div class="h-NewUser form">
            <div class="h-MandatoryReminderMode" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez indiquer au moins un mode de communication.</label>
                </div>
            </div>
            <div class="h-HomePhoneRequiredWithPhoneComn" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Le numéro de téléphone à la maison est obligatoire.</label>
                </div>
            </div>
            <div class="h-MandatoryTelNumber" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez indiquer au moins un numéro de téléphone.</label>
                </div>
            </div>

            <div class="h-EmailIsMandatory" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">L'adresse courriel est obligatoire.</label>
                </div>
            </div>
            <div class="h-EmailDoesNotExist" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez entrer une adresse courriel valide. Vérifiez qu'il n'y a pas d'erreur de saisie.</label>
                </div>
            </div>
            <div class="h-MobileNoIsMandatory" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Le numéro de téléphone mobile est obligatoire.</label>
                </div>
            </div>
            <div class="h-PhoneNumberIsInvalid" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Le numéro de téléphone saisi est invalide.</label>
                </div>
            </div>
            <div class="h-IdentificationError" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez remplir tous les champs.</label>
                </div>
            </div>

            <div class="row-fluid h-ContactInformation">
                <div class="span12">
                    <div class="control-group">
                        <div class="CIFAppointmentDate"><h1 class="titre-page">Prendre rendez-vous pour « consultation urgente » le&nbsp;jeudi&nbsp;12&nbsp;juin&nbsp;2025,&nbsp;13:15</h1></div>
                        <p class="sr-only">L'information de cette page peut être mise à jour sans rechargement.</p>
                        <div class="alert alert-primary timer-text centre">
                            <p class="remove-margin"><i class="fa fa-clock-o" aria-hidden="true"></i>&nbsp;Temps restant pour confirmer le rendez-vous: <span class="timer">00:02:55</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12" style="display: none">
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="FirstName">Numéro de référence </label>
                        <p class="summaryReferenceNumber"></p>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">

                    <div class="span6">
                        <div class="control-group">
                            <label class="control-label libelle-assure" for="FirstName">Prénom</label>
                            <p class="summaryFirstName">MANU</p>
                        </div>

                        <div class="clear"></div>

                        <div class="control-group">
                            <label class="control-label libelle-assure" for="LastName">Nom</label>
                            <p class="summaryLastName">JETTE</p>
                        </div>

                    </div>

                    <div class="span6">

                        <div class="control-group">
                            <label for="nam" class="libelle-assure">Numéro d'assurance maladie</label>
                            <p class="summaryNAM">JETM21081817</p>
                        </div>

                        <div class="clear"></div>

                        <div class="control-group">
                            <label for="Birthday" class="libelle-assure">Date de naissance</label>
                            <p class="summaryBirthday">18 août 2021</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactInformation">
                <div class="span12">
                    <div class="span6">
                        <div class="row-fluid h-IdentificationSubView" style="display: none;">
                            

<div class="h-IdentificationSubViewForm">
    <div class="row-fluid">
        <div class="control-group">
            <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_FirstName" class="control-label libelle-assure">Prénom</label>
            <div class="controls">
                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$IdentificationForm_FirstName" type="text" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_FirstName" class="input span12 h-FirstName submitOnKeyPress" autocomplete="off" maxlength="255">
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="control-group">
            <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_LastName" class="control-label libelle-assure">Nom</label>
            <div class="controls">
                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$IdentificationForm_LastName" type="text" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_LastName" class="input span12 h-LastName fix-margin-bottom submitOnKeyPress" autocomplete="off" maxlength="255">
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="control-group span12 h-DateOfbirthContainer">
            <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_Day" class="control-label libelle-assure">Date de naissance (jour mois année)</label>
            <div class="controls" data-standard-date-format="d-MM-yyyy" id="IdentificationForm_Birthday">
                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$IdentificationForm_Day" type="tel" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_Day" class="input IdentificationForm_Day inputRestriction submitOnKeyPress" autocomplete="off" maxlength="2">
                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_Month" class="control-label libelle-assure sr-only">Date de naissance (mois)</label>
                <select name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$IdentificationForm_Month" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_Month" class="IdentificationForm_Month">
	<option selected="selected" value="" disabled="" hidden="" label=" "></option>
	<option value="01">janvier</option>
	<option value="02">février</option>
	<option value="03">mars</option>
	<option value="04">avril</option>
	<option value="05">mai</option>
	<option value="06">juin</option>
	<option value="07">juillet</option>
	<option value="08">août</option>
	<option value="09">septembre</option>
	<option value="10">octobre</option>
	<option value="11">novembre</option>
	<option value="12">décembre</option>
</select>
                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_Year" class="control-label libelle-assure sr-only">Date de naissance (année)</label>
                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$IdentificationForm_Year" type="tel" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_IdentificationForm_Year" class="input IdentificationForm_Year inputRestriction submitOnKeyPress" autocomplete="off" maxlength="4">
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="control-group span12 h-SexeContainer">
            <label class="control-label libelle-assure">Sexe</label>
            <input value="M" name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$FemaleOrMale" type="radio" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_MaleGender" class="h-DetailledReport"><label class="control-label  libelle-Sexe" for="ctl00_ContentPlaceHolderMP_MaleGender">Homme</label>
            <input value="F" name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$IdentificationSubView$FemaleOrMale" type="radio" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_IdentificationSubView_FemaleGender" class="h-CondensedReport"><label class="control-label libelle-Sexe" for="ctl00_ContentPlaceHolderMP_FemaleGender">Femme</label>
        </div>
    </div>
</div>

                        </div>
                        <div class="control-group">
                            <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_ClientEmail" class="control-label libelle-assure">Courriel</label>
                            <div class="controls">
                                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$ClientInfo_ClientEmail" type="email" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_ClientEmail" class="span12 h-EmailTextBox ClientEmail continueOnKeyPress" autocomplete="off">
                                <div class="alert alert-danger h-Error h-EmailError" style="display: none;">L'adresse courriel saisie est invalide.</div>
                            </div>
                        </div>

                        <div class="control-group phoneNumbers">
                            <div class="controls span6">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_CellNumber" class="control-label libelle-assure">Téléphone mobile</label>
                                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$ClientInfo_CellNumber" type="tel" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_CellNumber" class="input-phone CellNumber continueOnKeyPress" data-majus="true" data-mask="************" autocomplete="off">
                            </div>
                            <div class="controls span6">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_HomeNumber" class="control-label libelle-assure">Téléphone à la maison</label>
                                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$ClientInfo_HomeNumber" type="tel" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_HomeNumber" class="input-phone HomeNumber continueOnKeyPress" data-majus="true" data-mask="************" autocomplete="off">
                            </div>
                        </div>
                        
                        <div class="control-group user__comments hidden">
                            <div class="controls span12 consent_margTpBt">
                                <label class="control-label libelle-assure">Consentement</label>
                                <label for="client_comment_CSTMT">
                                    <input class="checkbox" type="checkbox" id="client_comment_CSTMT"> La personne a consenti à ce que :	    <ul class="restore-list-styles">	        <li class="client_cmt_consent_li">la Régie de l’assurance maladie du Québec communique les renseignements permettant de l’identifier, les renseignements médicaux recueillis et, s’il y a lieu, les informations relatives à son professionnel traitant aux cliniques médicales et aux professionnels de la santé afin que sa demande de rendez-vous soit traitée;</li>	        <li class="client_cmt_consent_li">les cliniques médicales et les professionnels de la santé conservent et utilisent ses renseignements personnels afin d’améliorer les services liés à la gestion de ses demandes de rendez-vous.</li>        </ul>	
                                </label>
                            </div>
                        </div>
                        <div class="controls user__comments hidden" style="margin-bottom: 20px;">
                            <div class="activate_on_client_CSTMT">
                                <label for="userComments" class="libelle-assure">Quelle est la raison du rendez-vous? (facultatif)</label>
                                <textarea style="resize: none;" name="userComments" id="userComments" cols="30" rows="4" class="span12" maxlength="900" disabled=""></textarea>
                                <div class="pull-right">
                                    <p id="counter">0/1000</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="span6">
                        <div class="control-group well well2 mobile">
                            <fieldset>
                                <legend class="legend1em">Mode de communication des confirmations et des rappels</legend>
                                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$ClientInfo_EmailChecked" type="checkbox" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_EmailChecked" value="Courriel" class="EmailChecked">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_EmailChecked" class="inline">Courriel</label><!--Courriel-->
                                &nbsp;
                                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$ClientInfo_TextChecked" type="checkbox" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_TextChecked" value="Texto" class="TextChecked">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_TextChecked" class="inline">SMS</label><!--Texto (SMS)-->
                                &nbsp;
                                <input name="ctl00$ContentPlaceHolderMP$ClientInformationForm1$ClientInfo_PhoneChecked" type="checkbox" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_PhoneChecked" value="Phone" class="PhoneChecked">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_PhoneChecked" class="inline">Téléphone</label><!--Phone-->
                                <br>
                                <small class="h-SMSFees" style="display: none;">Des frais standards pour les SMS peuvent s'appliquer. Vérifiez les modalités de votre abonnement auprès de votre fournisseur.</small>
                            </fieldset>
                        </div>


                        <div class="control-group well well2">
                            <fieldset>
                                <legend class="legend1em">Langue de communication</legend>
                                <input value="French" name="communication" type="radio" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageF" class="LanguageF">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageF" class="inline">Français</label>
                                &nbsp;
                                <input value="English" name="communication" type="radio" id="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageE" class="LanguageE">
                                <label for="ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageE" class="inline">Anglais</label>
                            </fieldset>
                        </div>
                    </div>

                </div>

                <div class="row-fluid h-ContactInformation">
                    <div class="span12">
                        <div class="control-group centre">
                            <button type="button" class="btn btn-primary buttonContinueCIF">Continuer</button>
                        </div>
                    </div>
                </div>




            </div>
        </div>
    </div>
</div>



            </div>
        </div>

        <div class="h-View h-SummaryView" data-nav-en="summary" data-nav-fr="sommaire" data-context="Summary" style="display: none;">
            <div class="content centered">
                

<div class="summary">
    <div class="container-fluid">
        <div class="h-NewUser form">
            <div class="h-ExceededCancellationDeadline" style="display: none;">
                <div class="alert alert-error">
                <label class="control-label" id="ExceededCancellationDeadline" for="ClientInformationForm_ErrorMessage"></label>
                </div>
            </div>

            <div class="h-ErrorMessage" style="display: none;">
                <div class="alert alert-error">
                <i class="fa fa-info-circle" aria-hidden="true"></i>
                <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                <label class="control-label" for="ClientInformationForm_ErrorMessage"></label>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">
                    <div class="control-group">
                        <div class="summaryAppointmentDate"></div>                        

                        <div class="alert alert-primary timer-text centre">
                            <p class="remove-margin"><i class="fa fa-clock-o" aria-hidden="true"></i>&nbsp;Temps restant pour confirmer le rendez-vous: <span class="timer">00:02:55</span></p>                            
                        </div>
                    </div>
                </div>
            </div>            
             <div class="row-fluid h-ContactSummary">
                <div class="span12" style="display:none"> 
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="FirstName"> Numéro de référence </label>
                        <p class="summaryReferenceNumber"></p>
                        </div>
                 </div>
             </div>

            <div class="row-fluid h-ContactSummary">
                 
                <div class="span12">
                <div class="span6">
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="FirstName"> Prénom</label>
                        <p class="summaryFirstName">MANU</p>
                    </div>

                    <div class="clear"></div>

                    <div class="control-group">
                        <label class="control-label libelle-assure" for="LastName"> Nom</label>
                        <p class="summaryLastName">JETTE</p>
                    </div>
                </div>

                <div class="span6">
                    <div class="control-group">
                        <label for="nam" class="libelle-assure"> Numéro d'assurance maladie</label>
                        <p class="summaryNAM">JETM21081817</p>
                    </div>
                    
                    <div class="clear"></div>

                    <div class="control-group">
                        <label for="Birthday" class="libelle-assure"> Date de naissance</label>
                        <p class="summaryBirthday">18 août 2021</p> 
                    </div>
                </div>
                </div>
            </div>


            <div class="row-fluid h-ContactSummary">
              <div class="span12">
                <div class="span6">
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="Summary_ClientEmail"> Courriel</label>
                        <p class="summaryEmail"></p>
                    </div>
                    
                    <div class="clear"></div>

                    <div class="control-group">
                        <label class="control-label libelle-assure" for="Summary_CellNumber"> Téléphone mobile</label>
                        <p class="summaryCellNumber"></p>
                    </div>

                    <div class="clear"></div>

                    <div class="control-group">
                        <label class="control-label libelle-assure" for="Summary_HomeNumber"> Téléphone à la maison</label>
                        <p class="summaryHomeNumber"></p>
                    </div>  

                    <div class="clear"></div>

                    <div id="userCommentSummary" class="control-group">
                        <label class="control-label libelle-assure">Raison du rendez-vous</label>
                        <xmp class="summaryUserComments summaryUserCommentsPre"></xmp>
                    </div>
                </div>
                

                <div class="span6">
                    <div class="control-group">
                        <label for="medium" class="libelle-assure"> Confirmations et rappels</label>
                        <p class="summaryMedium">
                            
                        </p>
                    </div>

                    <div class="clear"></div>

                    <div class="control-group">
                        <p><label for="language" class="libelle-assure">Langue de communication</label></p>
                        <p class="summaryLanguage"></p>
                    </div>                   
                </div>
              </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">
                    <div class="infoCancelRdv alert alert-warning" style="display: none;">
                        <div class="control-group remove-margin">
                            <i class="fa fa-info-circle" aria-hidden="true"></i>
                            &nbsp;
                            <b>NOTES</b>
                            &nbsp;
                            <div class="h-annulationDiv">
                            <ul class="h-annulationText"><li>Lors de ce rendez-vous, la clinique pourrait vous confier aux soins d’un autre professionnel de la santé. De plus, elle se réserve le droit de vous imposer des frais en cas de retard, d’absence ou d’annulation.</li><li>Vous ne pouvez annuler le rendez-vous en ligne à moins de 24 heures d’avis. Au-delà de cette période, vous devez téléphoner directement à la clinique au numéro affiché ci-dessus.</li><li>Au moment du rendez-vous, vous devez présenter votre carte d’assurance maladie valide. Si vous oubliez de la présenter ou si elle est expirée, vous devrez payer pour recevoir des <a href="https://www.ramq.gouv.qc.ca/fr/citoyens/assurance-maladie/services-couverts/Pages/services-medicaux.aspx" target="_blank">services couverts</a>.</li></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">
                    <div class="control-group centre">
                        <button type="button" class="btn btn-primary buttonConfirmAppointment">Confirmer le rendez-vous</button> 
                    </div>
                </div>
            </div> 

        </div>
    </div>
</div>
            </div>
        </div>


        <div class="h-UpdateCancelAppointment">
            


<div class="updateCancelAppointment">
    <div class="h-View h-updateSearch h-SearchApptForUpdate" data-nav-en="search-appt" data-nav-fr="recherche-rdv" data-context="SearchApptForUpdate" style="display: none">
        <div class="row-fluid">
            <div class="span12 h-ErrorMessage" style="display: none;">
                <div class="alert alert-error">
                    <label class="ErrorMessageLabel"></label>
                </div>
            </div>
            <div class="span12 noLeft-margin">
                <h1 class="titre-page" id="findAppointmentTitle">Mettre à jour mes coordonnées ou annuler mon rendez-vous du</h1>

                <div class="control-group">
                    <label for="Input_ReferenceNumber">Numéro de référence</label>
                    <div class="controls">
                        <input type="tel" id="Input_ReferenceNumber" data-mask="AAAA AAAA AAAA" class="fix-margin-left updateSearchOnKeyPress" maxlength="14" autocomplete="off">
                        
                        
                        <div class="alert alert-danger h-Error h-EmailError" style="display: none;">L'adresse courriel saisie est invalide.</div>
                    </div>
                </div>

                <div class="control-group centre btnSearch">
                    <button type="button" class="btn btn-primary h-SearchApptButton">Rechercher</button>
                </div>
                <div class="control-group centre btnBack" style="display: none">
                    <button type="button" class="btn h-BackButton">Retour</button>
                </div>
            </div>
        </div>
    </div>

    <div class="h-View h-UpdateSearchResult" data-nav-en="update-appt" data-nav-fr="mise-a-jour-rdv" data-context="UpdateAppointment" style="display: none">
        <div class="row-fluid">
            

<div class="summary">
    <div class="container-fluid">
        <div class="h-NewUser form">
            <div class="h-ExceededCancellationDeadline" style="display: none;">
                <div class="alert alert-error">
                <label class="control-label" id="ExceededCancellationDeadline" for="ClientInformationForm_ErrorMessage"></label>
                </div>
            </div>

            <div class="h-ErrorMessage" style="display: none;">
                <div class="alert alert-error">
                <i class="fa fa-info-circle" aria-hidden="true"></i>
                <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                <label class="control-label" for="ClientInformationForm_ErrorMessage"></label>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">
                    <div class="control-group">
                        <div class="summaryAppointmentDate"></div>                        

                        <div class="alert alert-primary timer-text centre">
                            <p class="remove-margin"><i class="fa fa-clock-o" aria-hidden="true"></i>&nbsp;Temps restant pour confirmer le rendez-vous: <span class="timer"></span></p>                            
                        </div>
                    </div>
                </div>
            </div>            
             <div class="row-fluid h-ContactSummary">
                <div class="span12" style="display:none"> 
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="FirstName"> Numéro de référence </label>
                        <p class="summaryReferenceNumber"></p>
                        </div>
                 </div>
             </div>

            <div class="row-fluid h-ContactSummary">
                 
                <div class="span12">
                <div class="span6">
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="FirstName"> Prénom</label>
                        <p class="summaryFirstName"></p>
                    </div>

                    <div class="clear"></div>

                    <div class="control-group">
                        <label class="control-label libelle-assure" for="LastName"> Nom</label>
                        <p class="summaryLastName"></p>
                    </div>
                </div>

                <div class="span6">
                    <div class="control-group">
                        <label for="nam" class="libelle-assure"> Numéro d'assurance maladie</label>
                        <p class="summaryNAM"></p>
                    </div>
                    
                    <div class="clear"></div>

                    <div class="control-group">
                        <label for="Birthday" class="libelle-assure"> Date de naissance</label>
                        <p class="summaryBirthday"></p> 
                    </div>
                </div>
                </div>
            </div>


            <div class="row-fluid h-ContactSummary">
              <div class="span12">
                <div class="span6">
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="Summary_ClientEmail"> Courriel</label>
                        <p class="summaryEmail"></p>
                    </div>
                    
                    <div class="clear"></div>

                    <div class="control-group">
                        <label class="control-label libelle-assure" for="Summary_CellNumber"> Téléphone mobile</label>
                        <p class="summaryCellNumber"></p>
                    </div>

                    <div class="clear"></div>

                    <div class="control-group">
                        <label class="control-label libelle-assure" for="Summary_HomeNumber"> Téléphone à la maison</label>
                        <p class="summaryHomeNumber"></p>
                    </div>  

                    <div class="clear"></div>

                    <div id="userCommentSummary" class="control-group">
                        <label class="control-label libelle-assure">Raison du rendez-vous</label>
                        <xmp class="summaryUserComments summaryUserCommentsPre"></xmp>
                    </div>
                </div>
                

                <div class="span6">
                    <div class="control-group">
                        <label for="medium" class="libelle-assure"> Confirmations et rappels</label>
                        <p class="summaryMedium">
                            
                        </p>
                    </div>

                    <div class="clear"></div>

                    <div class="control-group">
                        <p><label for="language" class="libelle-assure">Langue de communication</label></p>
                        <p class="summaryLanguage"></p>
                    </div>                   
                </div>
              </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">
                    <div class="infoCancelRdv alert alert-warning" style="display: none;">
                        <div class="control-group remove-margin">
                            <i class="fa fa-info-circle" aria-hidden="true"></i>
                            &nbsp;
                            <b>NOTES</b>
                            &nbsp;
                            <div class="h-annulationDiv">
                            <ul class="h-annulationText"><li>Lors de ce rendez-vous, la clinique pourrait vous confier aux soins d’un autre professionnel de la santé. De plus, elle se réserve le droit de vous imposer des frais en cas de retard, d’absence ou d’annulation.</li><li>Vous ne pouvez annuler le rendez-vous en ligne à moins de 24 heures d’avis. Au-delà de cette période, vous devez téléphoner directement à la clinique au numéro affiché ci-dessus.</li><li>Au moment du rendez-vous, vous devez présenter votre carte d’assurance maladie valide. Si vous oubliez de la présenter ou si elle est expirée, vous devrez payer pour recevoir des <a href="https://www.ramq.gouv.qc.ca/fr/citoyens/assurance-maladie/services-couverts/Pages/services-medicaux.aspx" target="_blank">services couverts</a>.</li></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">
                    <div class="control-group centre">
                        <button type="button" class="btn btn-primary buttonConfirmAppointment">Confirmer le rendez-vous</button> 
                    </div>
                </div>
            </div> 

        </div>
    </div>
</div>
             <div class="row-guild h-ContactSummary" style="margin-bottom: 60px;">
                <div class="span12">
                    <p>Au moment du rendez-vous, vous devez présenter votre carte d’assurance maladie valide. Si vous oubliez de la présenter ou si elle est expirée, vous devrez payer pour recevoir des <a href="https://www.ramq.gouv.qc.ca/fr/citoyens/assurance-maladie/services-couverts/Pages/services-medicaux.aspx" target="_blank">services couverts</a>.</p>
                </div>
            </div>
            <div class="control-group centre btn-update-grp">
                <button type="button" id="ButtonUpdateMyInfo" class="btn btn-primary h-UpdateMyInfoButton button-block">Mettre à jour mes coordonnées</button>
                <button type="button" id="ButtonCancelAppointment" class="btn btn-danger h-ButtonCancelAppointment fix-btn-danger-margin button-block">Annuler mon rendez-vous</button>
            </div>
        </div>
    </div>

	<div class="h-View h-updateInfoForm" data-nav-en="update-contact-info" data-nav-fr="mise-a-jour-info-contact" data-context="UpdateContactInfo" style="display: none">
        <div class="row-fluid">
            

<div class="clientInformationForm">
    <div class="row-fluid">
        <div class="h-NewUser form">
            <div class="h-MandatoryReminderMode" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez indiquer au moins un mode de communication.</label>
                </div>
            </div>
            <div class="h-HomePhoneRequiredWithPhoneComn" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Le numéro de téléphone à la maison est obligatoire.</label>
                </div>
            </div>
            <div class="h-MandatoryTelNumber" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez indiquer au moins un numéro de téléphone.</label>
                </div>
            </div>

            <div class="h-EmailIsMandatory" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">L'adresse courriel est obligatoire.</label>
                </div>
            </div>
            <div class="h-EmailDoesNotExist" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez entrer une adresse courriel valide. Vérifiez qu'il n'y a pas d'erreur de saisie.</label>
                </div>
            </div>
            <div class="h-MobileNoIsMandatory" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Le numéro de téléphone mobile est obligatoire.</label>
                </div>
            </div>
            <div class="h-PhoneNumberIsInvalid" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Le numéro de téléphone saisi est invalide.</label>
                </div>
            </div>
            <div class="h-IdentificationError" style="display: none;">
                <div class="alert alert-error">
                    <i class="fa fa-hand-paper-o" aria-hidden="true"></i>
                    <span class="ErrorImgCIG focusOnShow OupsImgAssure">Attention!</span>
                    <label class="control-label" for="ClientInformationForm_ErrorMessage">Veuillez remplir tous les champs.</label>
                </div>
            </div>

            <div class="row-fluid h-ContactInformation">
                <div class="span12">
                    <div class="control-group">
                        <div class="CIFAppointmentDate"><h1 class="titre-page">Prendre rendez-vous pour « consultation urgente » le&nbsp;jeudi&nbsp;12&nbsp;juin&nbsp;2025,&nbsp;13:15</h1></div>
                        <p class="sr-only">L'information de cette page peut être mise à jour sans rechargement.</p>
                        <div class="alert alert-primary timer-text centre">
                            <p class="remove-margin"><i class="fa fa-clock-o" aria-hidden="true"></i>&nbsp;Temps restant pour confirmer le rendez-vous: <span class="timer"></span></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12" style="display: none">
                    <div class="control-group">
                        <label class="control-label libelle-assure" for="FirstName">Numéro de référence </label>
                        <p class="summaryReferenceNumber"></p>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactSummary">
                <div class="span12">

                    <div class="span6">
                        <div class="control-group">
                            <label class="control-label libelle-assure" for="FirstName">Prénom</label>
                            <p class="summaryFirstName"></p>
                        </div>

                        <div class="clear"></div>

                        <div class="control-group">
                            <label class="control-label libelle-assure" for="LastName">Nom</label>
                            <p class="summaryLastName"></p>
                        </div>

                    </div>

                    <div class="span6">

                        <div class="control-group">
                            <label for="nam" class="libelle-assure">Numéro d'assurance maladie</label>
                            <p class="summaryNAM"></p>
                        </div>

                        <div class="clear"></div>

                        <div class="control-group">
                            <label for="Birthday" class="libelle-assure">Date de naissance</label>
                            <p class="summaryBirthday"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row-fluid h-ContactInformation">
                <div class="span12">
                    <div class="span6">
                        <div class="row-fluid h-IdentificationSubView">
                            

<div class="h-IdentificationSubViewForm">
    <div class="row-fluid">
        <div class="control-group">
            <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_FirstName" class="control-label libelle-assure">Prénom</label>
            <div class="controls">
                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$IdentificationForm_FirstName" type="text" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_FirstName" class="input span12 h-FirstName submitOnKeyPress" autocomplete="off" maxlength="255">
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="control-group">
            <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_LastName" class="control-label libelle-assure">Nom</label>
            <div class="controls">
                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$IdentificationForm_LastName" type="text" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_LastName" class="input span12 h-LastName fix-margin-bottom submitOnKeyPress" autocomplete="off" maxlength="255">
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="control-group span12 h-DateOfbirthContainer">
            <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_Day" class="control-label libelle-assure">Date de naissance (jour mois année)</label>
            <div class="controls" data-standard-date-format="d-MM-yyyy" id="IdentificationForm_Birthday">
                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$IdentificationForm_Day" type="tel" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_Day" class="input IdentificationForm_Day inputRestriction submitOnKeyPress" autocomplete="off" maxlength="2">
                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_Month" class="control-label libelle-assure sr-only">Date de naissance (mois)</label>
                <select name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$IdentificationForm_Month" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_Month" class="IdentificationForm_Month">
	<option selected="selected" value="" disabled="" hidden="" label=" "></option>
	<option value="01">janvier</option>
	<option value="02">février</option>
	<option value="03">mars</option>
	<option value="04">avril</option>
	<option value="05">mai</option>
	<option value="06">juin</option>
	<option value="07">juillet</option>
	<option value="08">août</option>
	<option value="09">septembre</option>
	<option value="10">octobre</option>
	<option value="11">novembre</option>
	<option value="12">décembre</option>
</select>
                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_Year" class="control-label libelle-assure sr-only">Date de naissance (année)</label>
                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$IdentificationForm_Year" type="tel" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_IdentificationForm_Year" class="input IdentificationForm_Year inputRestriction submitOnKeyPress" autocomplete="off" maxlength="4">
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="control-group span12 h-SexeContainer">
            <label class="control-label libelle-assure">Sexe</label>
            <input value="M" name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$FemaleOrMale" type="radio" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_MaleGender" class="h-DetailledReport"><label class="control-label  libelle-Sexe" for="ctl00_ContentPlaceHolderMP_MaleGender">Homme</label>
            <input value="F" name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$IdentificationSubView$FemaleOrMale" type="radio" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_IdentificationSubView_FemaleGender" class="h-CondensedReport"><label class="control-label libelle-Sexe" for="ctl00_ContentPlaceHolderMP_FemaleGender">Femme</label>
        </div>
    </div>
</div>

                        </div>
                        <div class="control-group">
                            <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_ClientEmail" class="control-label libelle-assure">Courriel</label>
                            <div class="controls">
                                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$ClientInfo_ClientEmail" type="email" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_ClientEmail" class="span12 h-EmailTextBox ClientEmail continueOnKeyPress" autocomplete="off">
                                <div class="alert alert-danger h-Error h-EmailError" style="display: none;">L'adresse courriel saisie est invalide.</div>
                            </div>
                        </div>

                        <div class="control-group phoneNumbers">
                            <div class="controls span6">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_CellNumber" class="control-label libelle-assure">Téléphone mobile</label>
                                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$ClientInfo_CellNumber" type="tel" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_CellNumber" class="input-phone CellNumber continueOnKeyPress" data-majus="true" data-mask="************" autocomplete="off">
                            </div>
                            <div class="controls span6">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_HomeNumber" class="control-label libelle-assure">Téléphone à la maison</label>
                                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$ClientInfo_HomeNumber" type="tel" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_HomeNumber" class="input-phone HomeNumber continueOnKeyPress" data-majus="true" data-mask="************" autocomplete="off">
                            </div>
                        </div>
                        
                        <div class="control-group user__comments hidden">
                            <div class="controls span12 consent_margTpBt">
                                <label class="control-label libelle-assure">Consentement</label>
                                <label for="client_comment_CSTMT">
                                    <input class="checkbox" type="checkbox" id="client_comment_CSTMT"> La personne a consenti à ce que :	    <ul class="restore-list-styles">	        <li class="client_cmt_consent_li">la Régie de l’assurance maladie du Québec communique les renseignements permettant de l’identifier, les renseignements médicaux recueillis et, s’il y a lieu, les informations relatives à son professionnel traitant aux cliniques médicales et aux professionnels de la santé afin que sa demande de rendez-vous soit traitée;</li>	        <li class="client_cmt_consent_li">les cliniques médicales et les professionnels de la santé conservent et utilisent ses renseignements personnels afin d’améliorer les services liés à la gestion de ses demandes de rendez-vous.</li>        </ul>	
                                </label>
                            </div>
                        </div>
                        <div class="controls user__comments hidden" style="margin-bottom: 20px;">
                            <div class="activate_on_client_CSTMT">
                                <label for="userComments" class="libelle-assure">Quelle est la raison du rendez-vous? (facultatif)</label>
                                <textarea style="resize: none;" name="userComments" id="userComments" cols="30" rows="4" class="span12" maxlength="1000"></textarea>
                                <div class="pull-right">
                                    <p id="counter">0/1000</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="span6">
                        <div class="control-group well well2 mobile">
                            <fieldset>
                                <legend class="legend1em">Mode de communication des confirmations et des rappels</legend>
                                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$ClientInfo_EmailChecked" type="checkbox" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_EmailChecked" value="Courriel" class="EmailChecked">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_EmailChecked" class="inline">Courriel</label><!--Courriel-->
                                &nbsp;
                                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$ClientInfo_TextChecked" type="checkbox" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_TextChecked" value="Texto" class="TextChecked">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_TextChecked" class="inline">SMS</label><!--Texto (SMS)-->
                                &nbsp;
                                <input name="ctl00$ContentPlaceHolderMP$AssureUpdateAppointment$ClientInformationForm$ClientInfo_PhoneChecked" type="checkbox" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_PhoneChecked" value="Phone" class="PhoneChecked">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_PhoneChecked" class="inline">Téléphone</label><!--Phone-->
                                <br>
                                <small class="h-SMSFees" style="display: none;">Des frais standards pour les SMS peuvent s'appliquer. Vérifiez les modalités de votre abonnement auprès de votre fournisseur.</small>
                            </fieldset>
                        </div>


                        <div class="control-group well well2">
                            <fieldset>
                                <legend class="legend1em">Langue de communication</legend>
                                <input value="French" name="communication2" type="radio" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_LanguageF" class="LanguageF">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_LanguageF" class="inline">Français</label>
                                &nbsp;
                                <input value="English" name="communication2" type="radio" id="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_LanguageE" class="LanguageE">
                                <label for="ctl00_ContentPlaceHolderMP_AssureUpdateAppointment_ClientInformationForm_ClientInfo_LanguageE" class="inline">Anglais</label>
                            </fieldset>
                        </div>
                    </div>

                </div>

                <div class="row-fluid h-ContactInformation">
                    <div class="span12">
                        <div class="control-group centre">
                            <button type="button" class="btn btn-primary buttonContinueCIF">Continuer</button>
                        </div>
                    </div>
                </div>




            </div>
        </div>
    </div>
</div>



            <div class="control-group centre">
                <button type="button" id="ButtonUpdateSave" class="btn btn-primary">Enregistrer</button>
            </div>
        </div>
    </div>

    <div class="h-View h-cancelInfoForm" data-nav-en="cancel-appt" data-nav-fr="annuler-rdv" data-context="CancelAppointment" style="display: none">
        <div class="row-fluid">
            <div class="span12">
                <div class="control-group">
                    <div id="cancelAppointmentDate"></div>
                </div>
                <div class="infoCancelRdv alert alert-error" style="display: none;">
                    <div class="pull-left">
                        <i class="fa fa-info-circle" aria-hidden="true"></i>
                    </div>
                    <div class="alert-label">
                        Cette annulation est irréversible. <br> Désirez-vous continuer?
                    </div>
                </div>
                <br>
                
                <div class="control-group centre">
                    <button type="button" id="ButtonCancelRdv" class="btn btn-danger">Oui, annuler mon rendez-vous</button>
                    <button type="button" id="ButtonErrorAppointment" style="" class="btn h-BackButton">Continuer</button>
                    <button type="button" id="ButtonCancelRdvNo" class="btn h-BackButton">Non</button>
                </div>
            </div>
        </div>
    </div>

    <div class="h-View h-updateConfirmation" data-nav-en="confirm-update-appt" data-nav-fr="confirmation-mise-a-jour-rdv" data-context="ConfirmAppointmentUpdate" style="display: none">
        <div class="row-fluid">
            <div>
                <div class="control-group">
                    <div id="updateConfirmationDate"></div>                    
                    <h2 class="ss-titre-page">Confirmation</h2>
                </div>
            </div>
            <div style="display: none;">
                <div class="control-group">
                    <label class="control-label libelle-assure" for="FirstName">Numéro de référence </label>
                    <p class="summaryReferenceNumber"></p>
                </div>

                <div class="alert alert-success">
                    <div class="pull-left">
                        <i class="fa fa-check" aria-hidden="true"></i>
                    </div>
                    <div id="updateCompleted" class="alert-label"><p class="Interligne">Vos coordonnées ont été mises à jour avec succès.</p></div>
                </div>

                <div class="alert alert-info">
                    <div class="pull-left">
                        <i class="fa fa-info-circle" aria-hidden="true"></i>
                    </div>
                    <div class="alert-label">Vous devez mettre à jour vos coordonnées pour <b>chacun de vos rendez-vous.</b> Pour ce faire, vous devrez utiliser le numéro de référence qui correspond au rendez-vous concerné.</div>
                </div>

                <div class="control-group text-center">
                    <a href="/prendrerendezvous">Retour à la page d'identification</a>
                </div>
            </div>
        </div>
    </div>

    <div class="h-View h-cancelConfirmation" data-nav-en="confirm-cancel-appt" data-nav-fr="confirmer-annulation-rdv" data-context="ConfirmAppointmentCancellation" style="display:none;">
        <div class="row-fluid">
            <div class="span12">
                <div class="control-group">
                    <div id="CancelConfirmationDate"></div>                    
                    <h2 class="ss-titre-page">Confirmation</h2>
                </div>
                <div class="alert alert-success">
                      <div class="pull-left"><i class="fa fa-check" aria-hidden="true"></i> </div>
                      <div class="alert-label">Votre rendez-vous est annulé.</div>
                </div>

                <div class="control-group text-center">
                    <a href="/prendrerendezvous">Retour à la page d'identification</a>
                </div>

            </div>
        </div>
    </div>

</div>









        </div>

    </div>

    <div style="display: none;">
        <div class="h-Message_ApptReservationWillBeCancelled">La réservation de ce rendez-vous sera annulée. Voulez-vous continuer?</div>
    </div>
   

                </div>
            </div>
        </div>
        


        <!-- Modal Utilisation-Témoins -->
        <div id="utilisationTemoins" class="container hide">
            <div class="navbar-fixed-bottom utilCookie_margWidthHeight">

                <div class="modal-content modalBgColor-white">
                    <div class="modalHeaderPadding">
                        <h4 class="modal-title cookie_title texte_bleu_fonce">Utilisation de témoins de navigation</h4>
                    </div>
                    <div class="modal-body modalBodyPadding texte_bleu_fonce">
                        <p class="cookie_text">La Régie de l’assurance maladie du Québec utilise des fichiers témoins <em>(cookies)</em> pour optimiser votre expérience sur ce site Web, dont elle est responsable. Certains de ces témoins sont essentiels au bon fonctionnement du site, d’autres servent à des fins de statistiques et d’amélioration de nos services. Vous êtes libre de choisir les témoins non essentiels que vous acceptez d’activer.</p>
                    </div>
                    <div class="modalFooterPadding flex_container">
                        <div class="div_btn_toutAcceptBleu div_btn_pad">
                            <button id="btnToutAccepter" type="button" class="btn btn-block btn_bleu_popup">TOUT ACCEPTER</button>
                        </div>
                        <div class="div_btn_paramTemoins div_btn_pad">
                            <button type="button" class="btn btn-block btn_blanc_popup" data-toggle="modal" data-target="#paramTemoins" data-backdrop="static">PARAMÉTRER LES TÉMOINS</button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!-- Modal Utilisation-Témoins - FIN -->
        <!-- Modal Param-Témoins -->
        <div id="paramTemoins" class="modal fade" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modaldialogBS transform-pour-centrer paramTemoins_width">

                <div class="modal-content modalBgColor-white">
                    <div class="modalHeaderPadding">
                        <h4 class="modal-title cookie_title texte_bleu_fonce">Paramétrer les témoins</h4>
                    </div>
                    <div class="modal-body modalBodyPadding texte_bleu_fonce">
                        <p class="cookie_text">La Régie de l’assurance maladie du Québec utilise des fichiers témoins <em>(cookies)</em> pour optimiser votre expérience sur ce site Web. Certains de ces témoins sont essentiels au bon fonctionnement du site, d’autres servent à des fins de statistiques et d’amélioration de nos services. Vous êtes libre de choisir les témoins non essentiels que vous acceptez d’activer.</p>
                        <div class="checkbox paddingTop15">
                            <label class="checkbox_text_padding">
                                <input type="checkbox" class="cookie_checkbox" checked="checked" disabled="disabled" id="_ck_o" name="_ck_o" value="1">
                                <span class="cookie_checkbox_title">Témoins essentiels (obligatoire)</span>
                            </label>
                            <p class="checkbox_text_padding cookie_text">Certains témoins sont essentiels pour organiser et afficher les pages de ce site Web de façon optimale en fonction de vos préférences. Ces témoins essentiels contribuent à rendre un site Web utilisable et sécuritaire en activant des fonctions de base comme les formulaires. Le site Web ne peut pas fonctionner correctement sans ces témoins.</p>
                        </div>
                        <div class="checkbox paddingTop15">
                            <label class="checkbox_text_padding">
                                <input type="checkbox" class="cookie_checkbox" id="_ck_p" name="_ck_p" value="2">
                                <span class="cookie_checkbox_title">Témoins de performance</span>
                            </label>
                            <p class="checkbox_text_padding cookie_text">Les témoins de performance servent à repérer des problèmes ainsi qu’à recueillir des informations et des statistiques sur les habitudes d’utilisation du contenu Web. Ces données anonymes sont analysées pour améliorer le fonctionnement de ce site Web.</p>
                        </div>
                        <p class="cookie_text">En savoir plus sur la <a class="texte_bleu" href="../info/confidentialite.html" target="_blank">politique de confidentialité</a>.</p>
                    </div>
                    <div class="modalFooterPadding flex_container">
                        <div class="div_btn_confirmSel div_btn_pad">
                            <button id="btnConfirmSelect" type="button" class="btn btn-block btn_bleu_popup" data-dismiss="modal">CONFIRMER LA SÉLECTION</button>
                        </div>
                        <div class="div_btn_toutAcceptBlanc div_btn_pad">
                            <button id="btnToutAccepterParams" type="button" class="btn btn-block btn_blanc_popup" data-dismiss="modal">TOUT ACCEPTER</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!-- Modal Param-Témoins - FIN -->


        <!-- Pied de page – DÉBUT -->
        <footer role="contentinfo">
            <div class="container-fluid">
                <div class="footer-menu">
                    <div class="table-like">
                        <div class="col col-2">
                            <ul class="list-unstyled">
                                <li><a target="_blank" href="../info/accessibilite.html">Accessibilité</a></li>
                                <li><a target="_blank" href="../info/aide.html">Aide</a></li>
                                <li><a target="_blank" href="../Accueil/a-propos.html">À propos</a></li>
                                <li><a target="_blank" href="../info/confidentialite.html">Confidentialité et conditions d'utilisation</a></li>
                                <li><a target="_blank" href="../Accueil/accueil-prof.html">Professionnels de la santé</a></li>
                                <li><a href="#paramTemoins" data-toggle="modal" data-backdrop="static">Paramètres des témoins</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="copyright">
                    <div>
                        <a href="https://www.quebec.ca/" target="_blank"><img src="../Images/logoquebec.svg" width="100" alt="Gouvernement du Québec"></a>
                    </div>
                    <p><a href="https://www.quebec.ca/droit-auteur" target="_blank">©&nbsp;Gouvernement&nbsp;du&nbsp;Québec <img src="../Images/icone_externe_bleu.svg" alt="Cet hyperlien s'ouvrira dans une nouvelle fenêtre"></a></p>
                </div>
            </div>
        </footer>
        <!-- Pied de page – FIN -->
    
<div class="aspNetHidden">

	<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="FF7F136C">
	<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="/wEdABWmeN8hpy7TMuKEXfe5Jwb0eYl7m77tTWt6V0rv5Yv69j9qevTlKFYF0uaePMokN4e+mUiXwvrrhKAu2OsKbsmmdH8gKeXxWXw1LPU2pxu7EG1I+/2HOOXygUKpY5QJXb6IYWKkbZ4hmLw8X5fnZcGQKpHEeJ0od6DbLdza5jRIB/mgqlXuFg3MVrPcZZOYciJGkUwUCl+NhOwYYHvZy8rLNrLeFfQ4a1MukDHZLqsWbTFCcktZ8JqwnfTIZOB1l6Uio7nmKPBnhY575EVrH9tnoeiHDhfcgItFvYmaxVhLRndOTclGxQ6UvJx5ERkrIkPiUP464KeruzIURB0KpgQlkiP9mC0Yz1WMdi2HVBviNnTyEHxcrXVLeUpE8QoBEJb6Lh5VahDwmRwrQbMfeMS4T9dRqC6PJBQkBJTAUKGPJXZKngacj4VpJQnn0qzEct//kdGD2ur8Ueebp6/FOTX0syny60CowNG7hJ9hvrqOjQ==">
</div></form>
    <script>
        //À Ajouter à la fin de AssureMP.Master, juste avant </body>
        $(document).ready(function () {
            RDVS.init();
            model.init();                   
        });         

    </script>

    
    


<div class="waitingDialog" style="display: none;"></div><div id="greyCover" style="opacity: 0.8; z-index: 1100; position: fixed; top: 0px; bottom: 0px; right: 0px; left: 0px; display: none; background-color:#CECCCC;"></div><div id="greyCover" style="opacity: 0.8; z-index: 1100; position: fixed; top: 0px; bottom: 0px; right: 0px; left: 0px; display: none; background-color:#CECCCC;"></div></body></html>